{%- if section.blocks.size > 0 -%}
  {%- comment -%}
  ------------------------------------------------------------------------------------------------------------------------
  CSS
  ------------------------------------------------------------------------------------------------------------------------
  {%- endcomment -%}

  <style>
    #shopify-section-{{ section.id }} {
      {%- if section.settings.stack_collections -%}
        --collection-list-items-per-row: {{ section.settings.collections_per_row_mobile | times: 1 }};
      {%- else -%}
        --collection-list-item-size: 84vw;
      {%- endif -%}

      {%- if section.settings.space_items -%}
        --collection-list-gap: 1.5rem;
      {%- endif -%}
    }

    @media screen and (min-width: 700px) {
      #shopify-section-{{ section.id }} {
        {%- if section.settings.stack_collections -%}
          --collection-list-items-per-row: {{ 2 | at_most: section.settings.collections_per_row_desktop }};
        {%- else -%}
          --collection-list-item-size: 62vw;
        {%- endif -%}
      }
    }

    @media screen and (min-width: 1150px) {
      #shopify-section-{{ section.id }} {
        --collection-list-item-size: unset;
        --collection-list-items-per-row: {{ section.settings.collections_per_row_desktop }};

        {%- if section.settings.space_items -%}
          --collection-list-gap: 1.875rem;
        {%- endif -%}
      }
    }
  </style>

  {%- comment -%}
  ------------------------------------------------------------------------------------------------------------------------
  LIQUID
  ------------------------------------------------------------------------------------------------------------------------
  {%- endcomment -%}

  {%- assign remove_vertical_spacing = false -%}

  {%- if section.settings.subheading == blank and section.settings.title == blank and section.settings.content == blank -%}
    {%- assign reduce_vertical_spacing = true -%}

    {%- unless section.settings.space_items -%}
      {%- assign remove_vertical_spacing = true -%}
    {%- endunless -%}
  {%- endif -%}

  {%- assign color_scheme_hash = section.settings.color_scheme.settings.background_gradient | default: section.settings.color_scheme.settings.background | md5 -%}

  <div class="{% unless remove_vertical_spacing %}section-spacing {% if reduce_vertical_spacing %}section-spacing--tight{% endif %}{% endunless %} color-scheme color-scheme--{{ section.settings.color_scheme.id }} color-scheme--bg-{{ color_scheme_hash }} {% if section.settings.separate_section_with_border %}bordered-section{% endif %}">
    <div class="{% if section.settings.space_items %}container{% endif %}">
      <div class="section-stack">
        {%- render 'section-header', subheading: section.settings.subheading, heading: section.settings.title, content: section.settings.content, text_alignment: 'center' -%}

        {% capture collection_list %}
          {%- for block in section.blocks -%}
            {%- assign collection = block.settings.collection -%}
            {%- assign collection_url = block.settings.button_link | default: collection.url -%}

            {%- capture collection_card_info -%}
              <div class="collection-card__content color-scheme color-scheme--{{ block.settings.color_scheme.id }} prose prose--tight {{ block.settings.content_position }}">
                {%- if block.settings.subheading != blank -%}
                  <p class="h6">{{- block.settings.subheading -}}</p>
                {%- endif -%}

                {%- if block.settings.title != blank -%}
                  <p class="h3">{{- block.settings.title -}}</p>
                {%- endif -%}

                {%- if block.settings.button_text != blank -%}
                  {%- render 'button', type: 'button', content: block.settings.button_text, style: block.settings.button_style, focusable: false -%}
                {%- endif -%}
              </div>
            {%- endcapture -%}

            {%- capture collection_card_content -%}
              <div class="content-over-media content-over-media--{{ section.settings.image_size }}" style="--content-over-media-overlay: {{ block.settings.overlay_color.rgb }} / {{ block.settings.overlay_opacity | divided_by: 100.0 }};">
                {%- assign image = block.settings.image | default: collection.featured_image -%}
                {%- capture sizes -%}(max-width: 699px) 100vw, {% if block.settings.expand_collection %}100vw{% else %}(max-width: 1149px) {{ 100 | divided_by: section.settings.collections_per_row_desktop | at_least: 50 }}vw, {{ 100 | divided_by: section.settings.collections_per_row_desktop }}vw{% endif %}{%- endcapture -%}

                {%- if image != blank -%}
                  {{- image | image_url: width: image.width | image_tag: sizes: sizes, widths: '200,300,400,500,600,800,1000,1200,1400,1600,1800,2000,2200,2400', class: 'zoom-image group-hover:zoom', draggable: 'false' -}}
                {%- else -%}
                  {%- capture collection_placeholder -%}{%- cycle 'collection-1', 'collection-2', 'collection-3' -%}{%- endcapture -%}
                  {{- collection_placeholder | placeholder_svg_tag: 'placeholder zoom-image group-hover:zoom' | replace: '<svg', '<svg preserveAspectRatio="xMinYMin slice"' -}}
                {%- endif -%}

                {%- unless section.settings.show_text_outside -%}
                  {{- collection_card_info -}}
                {%- endunless -%}
              </div>

              {%- if section.settings.show_text_outside -%}
                {{- collection_card_info -}}
              {%- endif -%}
            {%- endcapture -%}

            {%- if collection_url != blank -%}
              <a href="{{ collection_url }}" class="collection-card {% if block.settings.expand_collection %}grow{% endif %} {% unless section.settings.stack_collections %}shrink-0 snap-center{% endunless %} {% if forloop.first %}is-selected{% endif %} group" {{ block.shopify_attributes }}>
                {{- collection_card_content -}}
              </a>
            {%- else -%}
              <div class="collection-card {% if block.settings.expand_collection %}grow{% endif %} {% unless section.settings.stack_collections %}shrink-0 snap-center{% endunless %} {% if forloop.first %}is-selected{% endif %} group" {{ block.shopify_attributes }}>
                {{- collection_card_content -}}
              </div>
            {%- endif -%}
          {%- endfor -%}
        {% endcapture %}

        {% if section.settings.stack_collections or section.blocks.size <= 1 %}
          <div class="collection-list {% if section.settings.show_text_outside %}collection-list--text-outside{% endif %} wrap">
            {{ collection_list }}
          </div>
        {% else %}
          <div class="floating-controls-container floating-controls-container--inside floating-controls-container--on-hover">
            {%- assign carousel_id = 'carousel-' | append: section.id -%}

            <carousel-prev-button class="floating-controls-container__control" aria-controls="{{ carousel_id }}">
              <button type="button" class="prev-next-button prev-next-button--prev circle-button circle-button--lg hover:animate-icon-inline" disabled>
                <span class="sr-only">{{ 'general.accessibility.previous' | t }}</span>
                {%- render 'icon' with 'arrow-left', direction_aware: true -%}
              </button>
            </carousel-prev-button>

            <scroll-carousel id="{{ carousel_id }}" group-cells allow-drag class="collection-list scroll-area {% if section.settings.show_text_outside %}collection-list--text-outside{% endif %} bleed lg:unbleed">
              {{ collection_list }}
            </scroll-carousel>

            <carousel-next-button class="floating-controls-container__control" aria-controls="{{ carousel_id }}">
              <button type="button" class="prev-next-button prev-next-button--next circle-button circle-button--lg hover:animate-icon-inline">
                <span class="sr-only">{{ 'general.accessibility.next' | t }}</span>
                {%- render 'icon' with 'arrow-right', direction_aware: true -%}
              </button>
            </carousel-next-button>
          </div>
        {% endif %}
      </div>
    </div>
  </div>
{%- endif -%}

{% schema %}
{
  "name": "t:sections.collection_list.name",
  "class": "shopify-section--collection-list",
  "tag": "section",
  "disabled_on": {
    "templates": ["password"],
    "groups": ["header", "custom.overlay"]
  },
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:global.colors.scheme",
      "default": "scheme-1"
    },
    {
      "type": "checkbox",
      "id": "separate_section_with_border",
      "label": "t:global.section.separate_section_with_border",
      "default": true
    },
    {
      "type": "select",
      "id": "image_size",
      "label": "t:global.image.size",
      "info": "t:global.image.ratio_avoid_cropping_info",
      "options": [
        {
          "value": "auto",
          "label": "t:global.sizes.original_image_ratio"
        },
        {
          "value": "xs",
          "label": "t:global.sizes.x_small"
        },
        {
          "value": "sm",
          "label": "t:global.sizes.small"
        },
        {
          "value": "md",
          "label": "t:global.sizes.medium"
        },
        {
          "value": "lg",
          "label": "t:global.sizes.large"
        }
      ],
      "default": "auto"
    },
    {
      "type": "checkbox",
      "id": "show_text_outside",
      "label": "t:sections.collection_list.show_text_outside",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "space_items",
      "label": "t:sections.collection_list.space_items",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "stack_collections",
      "label": "t:sections.collection_list.stack_collections",
      "default": true
    },
    {
      "type": "select",
      "id": "collections_per_row_mobile",
      "label": "t:sections.collection_list.collections_per_row_mobile",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ],
      "default": "1"
    },
    {
      "type": "range",
      "id": "collections_per_row_desktop",
      "min": 1,
      "max": 5,
      "label": "t:sections.collection_list.collections_per_row_desktop",
      "default": 3
    },
    {
      "type": "header",
      "content": "t:global.section.header_category"
    },
    {
      "type": "inline_richtext",
      "id": "subheading",
      "label": "t:global.text.subheading"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "label": "t:global.text.heading"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "t:global.text.content"
    }
  ],
  "blocks": [
    {
      "type": "collection",
      "name": "t:sections.collection_list.blocks.collection.name",
      "settings": [
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "label": "t:global.colors.scheme",
          "default": "scheme-4"
        },
        {
          "type": "collection",
          "id": "collection",
          "label": "t:sections.collection_list.blocks.collection.collection"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:global.image.image",
          "info": "t:sections.collection_list.blocks.collection.image_size_recommendation"
        },
        {
          "type": "checkbox",
          "id": "expand_collection",
          "label": "t:sections.collection_list.blocks.collection.expand_to_fill_row",
          "info": "t:sections.collection_list.blocks.collection.expand_to_fill_row_info",
          "default": false
        },
        {
          "type": "select",
          "id": "content_position",
          "label": "t:global.position.content_position",
          "options": [
            {
              "value": "place-self-start text-start",
              "label": "t:global.position.top_left"
            },
            {
              "value": "place-self-start-center text-center",
              "label": "t:global.position.top_center"
            },
            {
              "value": "place-self-start-end text-end",
              "label": "t:global.position.top_right"
            },
            {
              "value": "place-self-center-start text-start",
              "label": "t:global.position.middle_left"
            },
            {
              "value": "place-self-center text-center",
              "label": "t:global.position.middle_center"
            },
            {
              "value": "place-self-center-end text-end",
              "label": "t:global.position.middle_right"
            },
            {
              "value": "place-self-end-start text-start",
              "label": "t:global.position.bottom_left"
            },
            {
              "value": "place-self-end-center text-center",
              "label": "t:global.position.bottom_center"
            },
            {
              "value": "place-self-end text-end",
              "label": "t:global.position.bottom_right"
            }
          ],
          "default": "place-self-end-start text-start"
        },
        {
          "type": "inline_richtext",
          "id": "subheading",
          "label": "t:global.text.subheading"
        },
        {
          "type": "inline_richtext",
          "id": "title",
          "label": "t:global.text.heading"
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "t:global.text.button_text",
          "default": "View products"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:global.text.button_link",
          "info": "t:sections.collection_list.blocks.collection.button_link_info"
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "t:global.text.button_style",
          "options": [
            {
              "value": "outline",
              "label": "t:global.text.link_style_options.outline"
            },
            {
              "value": "solid",
              "label": "t:global.text.link_style_options.solid"
            },
            {
              "value": "link",
              "label": "t:global.text.link_style_options.link"
            }
          ],
          "default": "solid"
        },
        {
          "type": "header",
          "content": "t:global.colors.category"
        },
        {
          "type": "color",
          "id": "overlay_color",
          "label": "t:global.colors.overlay_color",
          "default": "#000000"
        },
        {
          "type": "range",
          "id": "overlay_opacity",
          "label": "t:global.colors.overlay_opacity",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "default": 30
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.collection_list.presets.collection_list.name",
      "blocks": [
        {
          "type": "collection"
        },
        {
          "type": "collection"
        },
        {
          "type": "collection"
        }
      ]
    }
  ]
}
{% endschema %}
