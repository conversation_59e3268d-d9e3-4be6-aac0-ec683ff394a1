{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
HAINT-DEVELOPER NOTE: the slideshow is one of Prestige most complex sections due to the large number of settings it offers. We
highly recommend you to not edit the code unless you have fully reviewed and understood all the code. A simple change
may have important consequence on the section itself.
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

{%- assign color_scheme_hash = section.settings.color_scheme.settings.background_gradient | default: section.settings.color_scheme.settings.background | md5 -%}

<div class="section-spacing section-spacing--tight color-scheme color-scheme--{{ section.settings.color_scheme.id }} color-scheme--bg-{{ color_scheme_hash }}">
  <div class="container container--{{ section.settings.page_width }}">
    <div class="section-stack">
      {%- if section.settings.show_title -%}
        <div class="section-header justify-self-center text-center">
          <h1 class="h2">{{ page.title }}</h1>
        </div>
      {%- endif -%}

      {%- if page.content != empty %}
        <div class="prose">
          {{- page.content -}}
        </div>
      {%- endif -%}
    </div>
  </div>
</div>
{% schema %}
{
  "name": "t:sections.main_page.name",
  "class": "shopify-section--main-page",
  "tag": "section",
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:global.colors.scheme",
      "default": "scheme-1"
    },
    {
      "type": "checkbox",
      "id": "show_title",
      "label": "t:sections.main_page.show_title",
      "default": true
    },
    {
      "type": "select",
      "id": "page_width",
      "label": "t:sections.main_page.page_width",
      "options": [
        {
          "value": "xs",
          "label": "t:global.sizes.x_small"
        },
        {
          "value": "sm",
          "label": "t:global.sizes.small"
        },
        {
          "value": "md",
          "label": "t:global.sizes.medium"
        },
        {
          "value": "lg",
          "label": "t:global.sizes.large"
        },
        {
          "value": "full",
          "label": "t:global.sizes.full_width"
        }
      ],
      "default": "xs"
    }
  ]
}
{% endschema %}
{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
HAINT-DEVELOPER NOTE: the slideshow is one of Prestige most complex sections due to the large number of settings it offers. We
highly recommend you to not edit the code unless you have fully reviewed and understood all the code. A simple change
may have important consequence on the section itself.
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}
