{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
HAINT-DEVELOPER NOTE: the slideshow is one of Prestige most complex sections due to the large number of settings it offers. We
highly recommend you to not edit the code unless you have fully reviewed and understood all the code. A simple change
may have important consequence on the section itself.
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

<div class="password color-scheme color-scheme--{{ section.settings.color_scheme.id }}">
  {%- if section.settings.background_image -%}
    {{- section.settings.background_image | image_url: width: section.settings.background_image.width | image_tag: widths: '300,400,500,600,800,1000,1200,1400,1600,1800,2000,2200,2400', class: 'image-background' -}}
  {%- endif -%}

  <div class="container">
    <div class="password__inner">
      <div class="password__header">
        {%- capture logo_content -%}
          {%- if section.settings.logo != blank -%}
            {%- capture sizes -%}{{ section.settings.logo_max_width }}px{%- endcapture -%}
            {%- capture widths -%}{{ section.settings.logo_max_width | times: 2 | at_most: section.settings.logo.width }}, {{ section.settings.logo_max_width | times: 3 | at_most: section.settings.logo.width }}{%- endcapture -%}
            {%- capture style -%}--image-mobile-max-width: {{ section.settings.logo_mobile_max_width }}px; --image-max-width: {{ section.settings.logo_max_width }}px{%- endcapture -%}

            <span class="sr-only">{{ shop.name }}</span>
            {{- section.settings.logo | image_url: width: section.settings.logo.width | image_tag: sizes: sizes, style: style, widths: widths, class: 'password__logo-image constrained-image' -}}
          {%- else -%}
            <span class="password__logo-text h5">{{ shop.name }}</span>
          {%- endif -%}
        {%- endcapture -%}

        <h1 class="password__logo">{{ logo_content }}</h1>

        <button class="password__password-button text-with-icon" type="button" aria-controls="storefront-password-drawer">
          <span class="hidden sm:block text-sm">{{- 'password.storefront_access.enter_password' | t -}}</span>
          {%- render 'icon' with 'lock', width: 12 -%}
        </button>

        {%- capture storefront_password_form -%}
          {%- form 'storefront_password', class: 'form' -%}
            <p class="text-center">{{ 'password.storefront_access.instructions' | t }}</p>

            {%- if form.errors -%}
              {%- assign has_errors = true -%}
              {%- render 'banner', status: 'error', content: form.errors.messages['form'], text_alignment: 'center' -%}
            {%- endif -%}

            {%- assign password_label = 'password.storefront_access.password' | t -%}
            {%- assign submit_label = 'password.storefront_access.enter_store' | t -%}

            {%- render 'input', type: 'password', name: 'password', label: password_label, required: true -%}
            {%- render 'button', type: 'submit', content: submit_label, stretch: true -%}
          {%- endform -%}
        {%- endcapture -%}

        <x-drawer id="storefront-password-drawer" {% if has_errors %}open{% endif %} class="drawer drawer--center-body color-scheme color-scheme--dialog">
          <p class="h4" slot="header">{{ 'password.storefront_access.store_access' | t }}</p>
          {{- storefront_password_form -}}
        </x-drawer>
      </div>

      <div class="password__block-list">
        {%- for block in section.blocks -%}
          {%- case block.type -%}
            {%- when 'content' -%}
              <div class="password__content color-scheme color-scheme--{{ block.settings.color_scheme.id }}" {{ block.shopify_attributes }}>
                <div class="v-stack gap-6 sm:gap-8">
                  {%- if block.settings.title != blank or shop.password_message != blank -%}
                    <div class="v-stack gap-2 text-center">
                      {%- if block.settings.title != blank -%}
                        <p class="h3">{{ block.settings.title }}</p>
                      {%- endif -%}

                      {%- if shop.password_message != blank -%}
                        <p>{{ shop.password_message }}</p>
                      {%- endif -%}
                    </div>
                  {%- endif -%}

                  {%- if block.settings.show_newsletter_form -%}
                    {%- form 'customer', class: 'fieldset' -%}
                      {%- if form.posted_successfully? -%}
                        {%- assign success_message = 'general.newsletter.subscribed_successfully' | t -%}
                        {%- render 'banner', status: 'success', content: success_message, text_alignment: 'center' -%}
                      {%- else -%}
                        {%- if form.errors -%}
                          {%- capture error_message -%}{{ form.errors.translated_fields['email'] }} {{ form.errors.messages['email'] }}{%- endcapture -%}
                          {%- render 'banner', status: 'error', content: error_message, text_alignment: 'center' -%}
                        {%- endif -%}

                        <input type="hidden" name="contact[tags]" value="newsletter">

                        {%- assign email_label = 'general.newsletter.email' | t -%}
                        {%- render 'input', type: 'email', name: 'contact[email]', label: email_label, required: true -%}

                        {%- assign submit_button = 'general.newsletter.notify_me' | t -%}
                        {%- render 'button', type: 'submit', content: submit_button, size: 'xl', stretch: true, background: block.settings.button_background, text_color: block.settings.button_text_color -%}
                      {%- endif -%}
                    {%- endform -%}
                  {%- endif -%}
                </div>
              </div>

            {%- when 'social_media' -%}
              {%- capture social_media -%}
                {%- render 'social-media', layout: 'block' -%}
              {%- endcapture -%}

              {%- if social_media != blank -%}
                <div class="password__social color-scheme color-scheme--{{ block.settings.color_scheme.id }}" {{ block.shopify_attributes }}>
                  {{- social_media -}}
                </div>
              {%- endif -%}
          {%- endcase -%}
        {%- endfor -%}
      </div>

      <div class="password__footer">
        <div class="text-with-icon">
          <p class="text-sm">{{ 'password.general.powered_by' | t }}</p>
          {%- render 'icon' with 'shopify-logo' -%}
        </div>

        <div class="h-stack gap-1 text-sm">
          {{- 'password.general.store_owner' | t -}}
          <a href="/admin" target="_blank">
            <span class="link">{{ 'password.general.login' | t }}</span>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.main_password.name",
  "class": "shopify-section--main-password",
  "tag": "section",
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:global.colors.scheme",
      "default": "scheme-3"
    },
    {
      "type": "header",
      "content": "t:sections.main_password.logo_category"
    },
    {
      "type": "image_picker",
      "id": "logo",
      "label": "t:sections.main_password.logo",
      "info": "t:sections.main_password.logo_info"
    },
    {
      "type": "range",
      "id": "logo_max_width",
      "min": 50,
      "max": 300,
      "step": 5,
      "unit": "px",
      "label": "t:global.image.width",
      "default": 140
    },
    {
      "type": "range",
      "id": "logo_mobile_max_width",
      "min": 50,
      "max": 170,
      "step": 5,
      "unit": "px",
      "label": "t:global.image.mobile_width",
      "default": 100
    },
    {
      "type": "header",
      "content": "t:sections.main_password.background_category"
    },
    {
      "type": "image_picker",
      "id": "background_image",
      "label": "t:global.image.background_image",
      "info": "t:sections.main_password.background_image_size_recommendation"
    }
  ],
  "blocks": [
    {
      "type": "content",
      "name": "t:sections.main_password.blocks.content.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.main_password.blocks.content.instructions"
        },
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "label": "t:global.colors.scheme",
          "default": "scheme-2"
        },
        {
          "type": "inline_richtext",
          "id": "title",
          "label": "t:global.text.heading",
          "default": "Opening soon"
        },
        {
          "type": "checkbox",
          "id": "show_newsletter_form",
          "label": "t:sections.main_password.blocks.content.show_newsletter_form",
          "info": "t:sections.main_password.blocks.content.show_newsletter_form_info",
          "default": true
        }
      ]
    },
    {
      "type": "social_media",
      "name": "t:sections.main_password.blocks.social_media.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.main_password.blocks.social_media.instructions"
        },
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "label": "t:global.colors.scheme",
          "default": "scheme-2"
        }
      ]
    }
  ],
  "default": {
    "blocks": [
      {
        "type": "content"
      },
      {
        "type": "social_media"
      }
    ]
  }
}
{% endschema %}
{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
HAINT-DEVELOPER NOTE: the slideshow is one of Prestige most complex sections due to the large number of settings it offers. We
highly recommend you to not edit the code unless you have fully reviewed and understood all the code. A simple change
may have important consequence on the section itself.
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}
