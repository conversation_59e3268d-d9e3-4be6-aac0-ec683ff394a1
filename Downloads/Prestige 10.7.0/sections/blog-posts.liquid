{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
HAINT-DEVELOPER NOTE: the slideshow is one of Prestige most complex sections due to the large number of settings it offers. We
highly recommend you to not edit the code unless you have fully reviewed and understood all the code. A simple change
may have important consequence on the section itself.
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}
{%- assign color_scheme_hash = section.settings.color_scheme.settings.background_gradient | default: section.settings.color_scheme.settings.background | md5 -%}

<div class="section-spacing color-scheme color-scheme--{{ section.settings.color_scheme.id }} color-scheme--bg-{{ color_scheme_hash }} {% if section.settings.separate_section_with_border %}bordered-section{% endif %}">
  <div class="container container--lg">
    <div class="section-stack">
      {%- render 'section-header', subheading: section.settings.subheading, heading: section.settings.title, content: section.settings.content, text_alignment: 'center' -%}

      <blog-posts class="blog-post-list justify-center {% unless section.settings.stack_on_mobile %}blog-post-list--carousel scroll-area snap-x bleed md:unbleed{% endunless %}" {% if settings.stagger_blog_posts_apparition %}reveal-on-scroll="true"{% endif %}>
        {%- capture sizes -%}(max-width: 699px) 95vw, (max-width: 1149px) calc(100vw / 2), calc((80rem - (5rem * ({{ section.settings.blog.articles_count | default: 3 | at_most: 3 }} - 1) / 3){%- endcapture -%}

        {%- for article in section.settings.blog.articles limit: section.settings.posts_count -%}
          {%- render 'blog-post-card', article: article, blog: section.settings.blog, show_category: section.settings.show_category, show_date: section.settings.show_date, show_excerpt: section.settings.show_excerpt, show_read_more: section.settings.show_read_more, sizes: sizes, position: forloop.index -%}
        {%- else -%}
          {%- for i in (1..3) -%}
            {%- render 'blog-post-card', show_category: section.settings.show_category, show_excerpt: section.settings.show_excerpt -%}
          {%- endfor -%}
        {%- endfor -%}
      </blog-posts>

      {%- if section.settings.button_text != blank -%}
        <div class="justify-self-center">
          {%- assign button_link = section.settings.button_link | default: section.settings.blog.url -%}
          {%- render 'button', href: button_link, content: section.settings.button_text -%}
        </div>
      {%- endif -%}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.blog_posts.name",
  "class": "shopify-section--blog-posts",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "custom.overlay"]
  },
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:global.colors.scheme",
      "default": "scheme-1"
    },
    {
      "type": "checkbox",
      "id": "separate_section_with_border",
      "label": "t:global.section.separate_section_with_border",
      "default": true
    },
    {
      "type": "blog",
      "id": "blog",
      "label": "t:sections.blog_posts.blocks.blog.blog"
    },
    {
      "type": "range",
      "id": "posts_count",
      "min": 2,
      "max": 9,
      "label": "t:sections.blog_posts.blocks.blog.posts_count",
      "default": 3
    },
    {
      "type": "checkbox",
      "id": "stack_on_mobile",
      "label": "t:sections.blog_posts.stack_on_mobile",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_category",
      "label": "t:global.blog.show_category",
      "info": "t:global.blog.show_category_info",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_date",
      "label": "t:global.blog.show_date",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_excerpt",
      "label": "t:global.blog.show_excerpt",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_read_more",
      "label": "t:global.blog.show_read_more",
      "default": true
    },
    {
      "type": "header",
      "content": "t:global.section.header_category"
    },
    {
      "type": "inline_richtext",
      "id": "subheading",
      "label": "t:global.text.subheading"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "label": "t:global.text.heading",
      "default": "Blog posts"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "t:global.text.content"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:global.text.button_link",
      "info": "t:sections.blog_posts.button_link_info"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "t:global.text.button_text",
      "default": "View all"
    }
  ],
  "presets": [
    {
      "name": "t:sections.blog_posts.presets.blog_posts.name",
      "settings": {
        "blog": "news"
      }
    }
  ]
}
{% endschema %}

{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
HAINT-DEVELOPER NOTE: the slideshow is one of Prestige most complex sections due to the large number of settings it offers. We
highly recommend you to not edit the code unless you have fully reviewed and understood all the code. A simple change
may have important consequence on the section itself.
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}