 <style>
  @media print {
    /* Hide anything but the main content when printing */
    *:has(~ #main), #main ~ * {
      display: none;
    }
  }
</style>

{%- assign color_scheme_hash = section.settings.color_scheme.settings.background_gradient | default: section.settings.color_scheme.settings.background | md5 -%}

<div class="section-spacing section-spacing--tight color-scheme color-scheme--{{ section.settings.color_scheme.id }} color-scheme--bg-{{ color_scheme_hash }}">
  <div class="container container--xxs">
    <div class="gift-card v-stack gap-12">
      <!-- GIFT CARD IMAGE -->
      {%- assign image = section.settings.image | default: gift_card.product.featured_media -%}

      {%- if image != blank -%}
        {{- image | image_url: width: image.width | image_tag: sizes: '270px', widths: '270,540,810', class: 'gift-card__image' -}}
      {%- else -%}
        <img src="{{ 'gift-card/card.svg' | shopify_asset_url }}" class="gift-card__image" loading="eager" alt="" width="270" height="180">
      {%- endif -%}

      <!-- GIFT CARD INFO -->
      <div class="v-stack gap-6 sm:gap-8">
        <div class="v-stack gap-5">
          <h1 class="h3 text-center">{{ 'gift_card.general.title' | t }}</h1>

          {% liquid
            if gift_card.expired or gift_card.enabled == false
              assign banner_error = 'gift_card.issued.expired' | t
              render 'banner', status: 'error', content: banner_error, text_alignment: 'center'
            else
              if settings.currency_code_enabled
                assign gift_card_balance = gift_card.balance | money_with_currency
                assign gift_card_initial_value = gift_card.initial_value | money_with_currency
              else
                assign gift_card_balance = gift_card.balance | money
                assign gift_card_initial_value = gift_card.initial_value | money
              endif

              if gift_card.balance == gift_card.initial_value
                assign banner_message = 'gift_card.issued.amount' | t: initial_value: gift_card_initial_value
              else
                assign banner_message = 'gift_card.issued.remaining_amount' | t: balance: gift_card_balance, initial_value: gift_card_initial_value
              endif

              render 'banner', status: 'success', content: banner_message, text_alignment: 'center'
            endif
          %}
        </div>

        <!-- REDEEM INFO -->
        <div class="gift-card__redeem-info bg-secondary text-center">
          <div class="v-stack gap-6">
            <div class="v-stack gap-5">
              <p>{{ 'gift_card.issued.redeem_instructions' | t }}</p>

              <input type="text" class="input text-center" value="{{ gift_card.code | format_code }}" readonly aria-label="{{ 'gift_card.issued.code' | t }}" onclick="this.select()">

              <div class="button-group button-group--same-width justify-center">
                <button type="button" class="button w-full" onclick="window.print()">{{ 'gift_card.general.print' | t }}</button>

                <copy-button data-text="{{ gift_card.code | escape }}" data-success-message="{{ 'gift_card.issued.copied' | t }}">
                  <button type="button" class="button button--secondary w-full">{{ 'gift_card.general.copy' | t }}</button>
                </copy-button>
              </div>
            </div>

            {%- if gift_card.expires_on -%}
              {%- assign expires_on = gift_card.expires_on | date: format: 'date' -%}
              <p class="heading text-subdued text-xxs">{{ 'gift_card.issued.expires_on' | t: expires_on: expires_on }}</p>
            {%- endif -%}
          </div>
        </div>

        <!-- SAVE OPTIONS -->
        {%- if gift_card.pass_url or section.settings.show_qr_code -%}
          <div class="gift-card__save">
            {%- if section.settings.show_qr_code -%}
              <p>{{ 'gift_card.issued.scan' | t }}</p>
              <qr-code class="gift-card__qr-code" identifier="{{ gift_card.qr_identifier }}"></qr-code>
            {%- endif -%}

            {%- if gift_card.pass_url -%}
              <a href="{{ gift_card.pass_url }}" class="gift-card__apple-wallet">
                <img src="{{ 'gift-card/add-to-apple-wallet.svg' | shopify_asset_url }}" width="145" height="45" alt="{{ 'gift_card.issued.add_to_apple_wallet' | t }}" loading="lazy">
              </a>
            {%- endif -%}
          </div>
        {%- endif -%}
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.main_gift_card.name",
  "class": "shopify-section--main-gift-card",
  "tag": "section",
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:global.colors.scheme",
      "default": "scheme-1"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.main_gift_card.gift_card_image",
      "info": "t:sections.main_gift_card.gift_card_image_info"
    },
    {
      "type": "checkbox",
      "id": "show_qr_code",
      "label": "t:sections.main_gift_card.show_qr_code",
      "default": true
    }
  ]
}
{% endschema %}
