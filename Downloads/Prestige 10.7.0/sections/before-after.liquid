<style>
  #shopify-section-{{ section.id }} {
    --before-after-initial-cursor-position: {{ section.settings.cursor_initial_position }}%;
    --before-after-cursor-background: {{ section.settings.cursor_background.rgb }};
  }
</style>

{%- if request.design_mode or section.blocks.size > 0 -%}
  {%- assign color_scheme_hash = section.settings.color_scheme.settings.background_gradient | default: section.settings.color_scheme.settings.background | md5 -%}

  <div class="section-spacing color-scheme color-scheme--{{ section.settings.color_scheme.id }} color-scheme--bg-{{ color_scheme_hash }} {% if section.settings.separate_section_with_border %}bordered-section{% endif %}">
    <div class="container container--lg">
      <div class="section-stack">
        {%- render 'section-header', subheading: section.settings.subheading, heading: section.settings.title, content: section.settings.content -%}

        {%- if request.design_mode and section.blocks.size < 2 -%}
          {%- assign error_message = 'sections.before_after.not_enough_blocks' | t -%}
          {%- render 'banner', content: error_message, status: 'error', text_alignment: 'center' -%}
        {%- endif -%}

        <before-after class="before-after before-after--{{ section.settings.cursor_direction }}" {% if section.settings.cursor_direction == 'vertical' %}vertical{% endif %}>
          {%- comment -%}
          ----------------------------------------------------------------------------------------------------------------
          BEFORE AFTER IMAGES
          ----------------------------------------------------------------------------------------------------------------
          {%- endcomment -%}

          {%- for block in section.blocks -%}
            {%- if block.type == 'before' -%}
              {%- assign container_class = 'before-after__before-image' -%}
              {%- assign image_class = 'w-full' -%}
            {%- else -%}
              {%- assign container_class = 'before-after__after-image' -%}
              {%- assign image_class = 'image-background' -%}
            {%- endif -%}

            <div class="{{ container_class }} color-scheme color-scheme--{{ block.settings.color_scheme.id }}" {{ block.shopify_attributes }}>
              {%- capture sizes -%}(max-width: 999px) 100vw, min(1260px, 100vw){%- endcapture -%}

              {%- if block.settings.image != blank -%}
                <picture>
                  {%- if block.settings.mobile_image -%}
                    <source
                        media="(max-width: 699px)"
                        srcset="{{ block.settings.mobile_image | image_url: width: 400 }} 400w, {{ block.settings.mobile_image | image_url: width: 600 }} 600w, {{ block.settings.mobile_image | image_url: width: 800 }} 800w, {{ block.settings.mobile_image | image_url: width: 1000 }} 1000w"
                        width="{{ block.settings.mobile_image.width }}"
                        height="{{ block.settings.mobile_image.height }}"
                    >
                  {%- endif -%}

                  {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: sizes: sizes, widths: '300,400,500,600,800,1000,1200,1400,1600,1800,2000,2200,2400', class: image_class, draggable: false -}}
                </picture>
              {%- else -%}
                {%- capture placeholder_image -%}{% cycle 'lifestyle-1', 'lifestyle-2' %}{%- endcapture -%}
                {%- capture placeholder_class -%}{% cycle 'placeholder', 'placeholder placeholder--invert' %}{%- endcapture -%}

                {{- placeholder_image | placeholder_svg_tag: placeholder_class | replace: '<svg', '<svg preserveAspectRatio="xMinYMin slice"' -}}
              {%- endif -%}

              {%- if block.settings.text != blank or block.settings.button_text != blank -%}
                <div class="before-after__content-wrapper v-stack {% if block.settings.link_style == 'link' %}gap-2{% else %}gap-4{% endif %}">
                  {%- if block.settings.text != blank -%}
                    <p class="h6 sm:h5">{{ block.settings.text }}</p>
                  {%- endif -%}

                  {%- if block.settings.link_text != blank -%}
                    {%- render 'button', href: block.settings.link_url, content: block.settings.link_text, style: block.settings.link_style, background: block.settings.button_background, text_color: block.settings.button_text_color -%}
                  {%- endif -%}
                </div>
              {%- endif -%}
            </div>
          {%- endfor -%}

          {%- comment -%}
          ----------------------------------------------------------------------------------------------------------------
          BEFORE AFTER CURSOR
          ----------------------------------------------------------------------------------------------------------------
          {%- endcomment -%}

          <div class="before-after__cursor" tabindex="0">
            <span class="sr-only">{{ 'sections.before_after.cursor_accessibility_text' | t }}</span>

            <svg role="presentation" focusable="false" fill="none" width="50" height="50" viewBox="0 0 50 50">
              <g {% if section.settings.cursor_direction == 'vertical' %}transform="rotate(90, 25, 25)"{% endif %}>
                <rect width="50" height="50" rx="25" fill="{{ section.settings.cursor_background }}"/>
                <path d="m19.25 19-6 6 6 6m11.5 0 6-6-6-6" stroke="{{ section.settings.cursor_arrows_color }}" stroke-width=".75" stroke-linecap="square"/>
              </g>
            </svg>
          </div>
        </before-after>
      </div>
    </div>
  </div>
{%- endif -%}

{% schema %}
{
  "name": "t:sections.before_after.name",
  "class": "shopify-section--before-after-image",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "custom.overlay"]
  },
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:global.colors.scheme",
      "default": "scheme-1"
    },
    {
      "type": "checkbox",
      "id": "separate_section_with_border",
      "label": "t:global.section.separate_section_with_border",
      "default": true
    },
    {
      "type": "inline_richtext",
      "id": "subheading",
      "label": "t:global.text.subheading"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "label": "t:global.text.heading",
      "default": "Before/after"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "t:global.text.content",
      "default": "<p>Showcase your product benefit by using before/after image comparison.</p>"
    },
    {
      "type": "header",
      "content": "t:sections.before_after.cursor_category"
    },
    {
      "type": "select",
      "id": "cursor_direction",
      "label": "t:sections.before_after.cursor_direction",
      "options": [
        {
          "value": "horizontal",
          "label": "t:sections.before_after.cursor_direction_options.horizontal"
        },
        {
          "value": "vertical",
          "label": "t:sections.before_after.cursor_direction_options.vertical"
        }
      ],
      "default": "horizontal"
    },
    {
      "type": "range",
      "id": "cursor_initial_position",
      "min": 0,
      "max": 100,
      "unit": "%",
      "label": "t:sections.before_after.cursor_initial_position",
      "default": 50
    },
    {
      "type": "color",
      "id": "cursor_background",
      "label": "t:global.colors.background",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "cursor_arrows_color",
      "label": "t:sections.before_after.cursor_arrows_color",
      "default": "#000000"
    }
  ],
  "blocks": [
    {
      "type": "before",
      "name": "t:sections.before_after.blocks.before.name",
      "limit": 1,
      "settings": [
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "label": "t:global.colors.scheme",
          "default": "scheme-4"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:global.image.image",
          "info": "t:sections.before_after.blocks.before.image_info"
        },
        {
          "type": "image_picker",
          "id": "mobile_image",
          "label": "t:global.image.mobile_image",
          "info": "t:sections.before_after.blocks.before.mobile_image_info"
        },
        {
          "type": "inline_richtext",
          "id": "text",
          "label": "t:global.text.text",
          "default": "Before"
        },
        {
          "type": "text",
          "id": "link_text",
          "label": "t:global.text.button_text"
        },
        {
          "type": "url",
          "id": "link_url",
          "label": "t:global.text.button_link"
        },
        {
          "type": "select",
          "id": "link_style",
          "label": "t:global.text.button_style",
          "options": [
            {
              "value": "outline",
              "label": "t:global.text.link_style_options.outline"
            },
            {
              "value": "solid",
              "label": "t:global.text.link_style_options.solid"
            },
            {
              "value": "link",
              "label": "t:global.text.link_style_options.link"
            }
          ],
          "default": "link"
        }
      ]
    },
    {
      "type": "after",
      "name": "t:sections.before_after.blocks.after.name",
      "limit": 1,
      "settings": [
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "label": "t:global.colors.scheme",
          "default": "scheme-4"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:global.image.image",
          "info": "t:sections.before_after.blocks.after.image_info"
        },
        {
          "type": "image_picker",
          "id": "mobile_image",
          "label": "t:global.image.mobile_image",
          "info": "t:sections.before_after.blocks.after.image_info"
        },
        {
          "type": "inline_richtext",
          "id": "text",
          "label": "t:global.text.text",
          "default": "After"
        },
        {
          "type": "text",
          "id": "link_text",
          "label": "t:global.text.button_text"
        },
        {
          "type": "url",
          "id": "link_url",
          "label": "t:global.text.button_link"
        },
        {
          "type": "select",
          "id": "link_style",
          "label": "t:global.text.button_style",
          "options": [
            {
              "value": "outline",
              "label": "t:global.text.link_style_options.outline"
            },
            {
              "value": "solid",
              "label": "t:global.text.link_style_options.solid"
            },
            {
              "value": "link",
              "label": "t:global.text.link_style_options.link"
            }
          ],
          "default": "link"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.before_after.presets.before_after.name",
      "blocks": [
        {
          "type": "before"
        },
        {
          "type": "after"
        }
      ]
    }
  ]
}
{% endschema %}
