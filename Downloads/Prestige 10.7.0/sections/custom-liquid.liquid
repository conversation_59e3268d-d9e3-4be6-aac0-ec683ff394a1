{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
HAINT-DEVELOPER NOTE: the slideshow is one of Prestige most complex sections due to the large number of settings it offers. We
highly recommend you to not edit the code unless you have fully reviewed and understood all the code. A simple change
may have important consequence on the section itself.
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

{%- if section.settings.liquid != blank -%}
  {%- assign color_scheme_hash = section.settings.color_scheme.settings.background_gradient | default: section.settings.color_scheme.settings.background | md5 -%}

  <div class="{% unless section.settings.remove_vertical_spacing %}section-spacing{% endunless %} color-scheme color-scheme--{{ section.settings.color_scheme.id }} color-scheme--bg-{{ color_scheme_hash }} {% if section.settings.separate_section_with_border %}bordered-section{% endif %}">
    <div class="{% unless section.settings.remove_horizontal_spacing %}container{% endunless %}">
      <div class="text-{{ section.settings.text_alignment }}">
        {{- section.settings.liquid -}}
      </div>
    </div>
  </div>
{%- endif -%}

{% schema %}
{
  "name": "t:sections.custom_liquid.name",
  "class": "shopify-section--custom-liquid",
  "tag": "section",
  "disabled_on": {
    "groups": ["custom.overlay"]
  },
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:global.colors.scheme",
      "default": "scheme-1"
    },
    {
      "type": "checkbox",
      "id": "separate_section_with_border",
      "label": "t:global.section.separate_section_with_border",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "remove_vertical_spacing",
      "label": "t:global.spacing.remove_vertical_spacing",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "remove_horizontal_spacing",
      "label": "t:global.spacing.remove_horizontal_spacing",
      "default": false
    },
    {
      "type": "liquid",
      "id": "liquid",
      "label": "t:global.code.liquid",
      "info": "t:global.code.liquid_info",
      "default": "<p>Write or copy/paste Liquid code</p>"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "t:global.text.alignment",
      "options": [
        {
          "value": "start",
          "label": "t:global.position.left"
        },
        {
          "value": "center",
          "label": "t:global.position.center"
        },
        {
          "value": "end",
          "label": "t:global.position.right"
        }
      ],
      "default": "center"
    }
  ],
  "presets": [
    {
      "name": "t:sections.custom_liquid.presets.custom_liquid.name"
    }
  ]
}
{% endschema %}

{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
HAINT-DEVELOPER NOTE: the slideshow is one of Prestige most complex sections due to the large number of settings it offers. We
highly recommend you to not edit the code unless you have fully reviewed and understood all the code. A simple change
may have important consequence on the section itself.
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}
