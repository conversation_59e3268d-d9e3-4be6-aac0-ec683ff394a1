{%- if section.blocks.size > 0 -%}
  {%- assign color_scheme_hash = section.settings.color_scheme.settings.background_gradient | default: section.settings.color_scheme.settings.background | md5 -%}

  <div class="section-spacing section-spacing--tight color-scheme color-scheme--{{ section.settings.color_scheme.id }} color-scheme--bg-{{ color_scheme_hash }} {% if section.settings.separate_section_with_border %}bordered-section{% endif %}">
    <div class="container">
      <div class="v-stack gap-8">
        {%- capture section_content -%}
          {%- for block in section.blocks -%}
            <div class="text-with-icons__item {% if forloop.first %}is-selected{% endif %} {% unless section.settings.stack_on_mobile %}snap-center{% endunless %}" role="group" aria-label="{{ 'general.accessibility.item_nth_of_count' | t: index: forloop.index, count: section.blocks.size }}" {{ block.shopify_attributes }}>
              <div class="v-stack gap-6 justify-items-center sm:justify-items-{{ section.settings.text_alignment }}">
                {%- liquid
                  assign loading_strategy = nil

                  if section.index <= 3 and forloop.position <= 3
                    assign loading_strategy = 'eager'
                  endif
                -%}

                {%- if block.settings.custom_icon != blank -%}
                  {%- capture sizes -%}{{ block.settings.icon_width }}px{%- endcapture -%}
                  {%- capture widths -%}{{ block.settings.icon_width }}, {{ block.settings.icon_width | times: 2 | at_most: block.settings.custom_icon.width }}{%- endcapture -%}
                  {%- capture style -%}--image-mobile-max-width: {{ block.settings.mobile_icon_width }}px; --image-max-width: {{ block.settings.icon_width }}px{%- endcapture -%}
                  {{- block.settings.custom_icon | image_url: width: block.settings.custom_icon.width | image_tag: loading: loading_strategy, sizes: sizes, widths: widths, class: 'constrained-image', style: style -}}
                {%- else -%}
                  <div style="{% render 'surface', text_color: section.settings.icon_color %}">
                    {%- render 'icon' with block.settings.icon, width: block.settings.mobile_icon_width, class: 'sm:hidden' -%}
                    {%- render 'icon' with block.settings.icon, width: block.settings.icon_width, class: 'hidden sm:block' -%}
                  </div>
                {%- endif -%}

                {%- if block.settings.title != blank or block.settings.content != blank -%}
                  <div class="v-stack gap-2 text-center sm:text-{{ section.settings.text_alignment }}">
                    {%- if block.settings.title != blank -%}
                      <p class="h6">{{ block.settings.title }}</p>
                    {%- endif -%}

                    {%- if block.settings.content != blank -%}
                      <div class="prose">
                        {{- block.settings.content -}}
                      </div>
                    {%- endif -%}
                  </div>
                {%- endif -%}
              </div>
            </div>
          {%- endfor -%}
        {%- endcapture -%}

        {%- if section.settings.stack_on_mobile -%}
          <div class="text-with-icons text-with-icons--stacked" role="region" style="{% render 'surface', text_color: section.settings.text_color %}">
            {{- section_content -}}
          </div>
        {%- else -%}
          {%- assign carousel_id = 'text-with-icons-' | append: section.id -%}

          <text-with-icons-carousel disabled-on="sm" allow-swipe id="{{ carousel_id }}" class="text-with-icons full-bleed sm:unbleed" role="region" style="{% render 'surface', text_color: section.settings.text_color %}">
            {{- section_content -}}
          </text-with-icons-carousel>

          {%- if section.blocks.size > 1 -%}
            <carousel-navigation aria-controls="{{ carousel_id }}" class="page-dots sm:hidden">
              {%- for i in (1..section.blocks.size) -%}
                <button class="tap-area" aria-current="{% if forloop.first %}true{% else %}false{% endif %}">
                  <span class="sr-only">{{ 'general.accessibility.go_to_item' | t: index: forloop.index }}</span>
                </button>
              {%- endfor -%}
            </carousel-navigation>
          {%- endif -%}
        {%- endif -%}
      </div>
    </div>
  </div>
{%- endif -%}

{% schema %}
{
  "name": "t:sections.text_with_icons.name",
  "class": "shopify-section--text-with-icons",
  "tag": "section",
  "max_blocks": 5,
  "disabled_on": {
    "groups": ["header", "custom.overlay"]
  },
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:global.colors.scheme",
      "default": "scheme-1"
    },
    {
      "type": "checkbox",
      "id": "separate_section_with_border",
      "label": "t:global.section.separate_section_with_border",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "stack_on_mobile",
      "label": "t:sections.text_with_icons.stack_on_mobile",
      "default": false
    },
    {
      "type": "color",
      "id": "icon_color",
      "label": "t:sections.text_with_icons.icon_color"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "t:global.text.text"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "t:global.text.alignment",
      "options": [
        {
          "value": "start",
          "label": "t:global.position.left"
        },
        {
          "value": "center",
          "label": "t:global.position.center"
        },
        {
          "value": "end",
          "label": "t:global.position.right"
        }
      ],
      "default": "center"
    }
  ],
  "blocks": [
    {
      "type": "item",
      "name": "t:sections.text_with_icons.blocks.item.name",
      "settings": [
        {
          "type": "select",
          "id": "icon",
          "label": "t:global.icons.icon",
          "options": [
            {
              "value": "picto-award-gift",
              "label": "t:global.icons.award_gift",
              "group": "t:global.icons.shop_category"
            },
            {
              "value": "picto-bag-handle",
              "label": "t:global.icons.bag_handle",
              "group": "t:global.icons.shop_category"
            },
            {
              "value": "picto-building",
              "label": "t:global.icons.building",
              "group": "t:global.icons.shop_category"
            },
            {
              "value": "picto-coupon",
              "label": "t:global.icons.coupon",
              "group": "t:global.icons.shop_category"
            },
            {
              "value": "picto-gift",
              "label": "t:global.icons.gift",
              "group": "t:global.icons.shop_category"
            },
            {
              "value": "picto-info",
              "label": "t:global.icons.info",
              "group": "t:global.icons.shop_category"
            },
            {
              "value": "picto-love",
              "label": "t:global.icons.love",
              "group": "t:global.icons.shop_category"
            },
            {
              "value": "picto-percent",
              "label": "t:global.icons.percent",
              "group": "t:global.icons.shop_category"
            },
            {
              "value": "picto-star",
              "label": "t:global.icons.star",
              "group": "t:global.icons.shop_category"
            },
            {
              "value": "picto-box",
              "label": "t:global.icons.box",
              "group": "t:global.icons.shipping_category"
            },
            {
              "value": "picto-delivery-truck",
              "label": "t:global.icons.delivery_truck",
              "group": "t:global.icons.shipping_category"
            },
            {
              "value": "picto-pin",
              "label": "t:global.icons.pin",
              "group": "t:global.icons.shipping_category"
            },
            {
              "value": "picto-plane",
              "label": "t:global.icons.plane",
              "group": "t:global.icons.shipping_category"
            },
            {
              "value": "picto-return",
              "label": "t:global.icons.return",
              "group": "t:global.icons.shipping_category"
            },
            {
              "value": "picto-credit-card",
              "label": "t:global.icons.credit_card",
              "group": "t:global.icons.payment_and_security_category"
            },
            {
              "value": "picto-lock",
              "label": "t:global.icons.lock",
              "group": "t:global.icons.payment_and_security_category"
            },
            {
              "value": "picto-money",
              "label": "t:global.icons.money",
              "group": "t:global.icons.payment_and_security_category"
            },
            {
              "value": "picto-secure-profile",
              "label": "t:global.icons.secure_profile",
              "group": "t:global.icons.payment_and_security_category"
            },
            {
              "value": "picto-shield",
              "label": "t:global.icons.shield",
              "group": "t:global.icons.payment_and_security_category"
            },
            {
              "value": "picto-earth",
              "label": "t:global.icons.earth",
              "group": "t:global.icons.ecology_category"
            },
            {
              "value": "picto-leaf",
              "label": "t:global.icons.leaf",
              "group": "t:global.icons.ecology_category"
            },
            {
              "value": "picto-recycle",
              "label": "t:global.icons.recycle",
              "group": "t:global.icons.ecology_category"
            },
            {
              "value": "picto-tree",
              "label": "t:global.icons.tree",
              "group": "t:global.icons.ecology_category"
            },
            {
              "value": "picto-at-sign",
              "label": "t:global.icons.at_sign",
              "group": "t:global.icons.tech_category"
            },
            {
              "value": "picto-bluetooth",
              "label": "t:global.icons.bluetooth",
              "group": "t:global.icons.tech_category"
            },
            {
              "value": "picto-camera",
              "label": "t:global.icons.camera",
              "group": "t:global.icons.tech_category"
            },
            {
              "value": "picto-printer",
              "label": "t:global.icons.printer",
              "group": "t:global.icons.tech_category"
            },
            {
              "value": "picto-smart-watch",
              "label": "t:global.icons.smart_watch",
              "group": "t:global.icons.tech_category"
            },
            {
              "value": "picto-wifi",
              "label": "t:global.icons.wifi",
              "group": "t:global.icons.tech_category"
            },
            {
              "value": "picto-avatar",
              "label": "t:global.icons.avatar",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-chat",
              "label": "t:global.icons.chat",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-calendar",
              "label": "t:global.icons.calendar",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-comment",
              "label": "t:global.icons.comment",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-customer-support",
              "label": "t:global.icons.customer_support",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-happy-face",
              "label": "t:global.icons.happy_face",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-mailbox",
              "label": "t:global.icons.mailbox",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-mobile-phone",
              "label": "t:global.icons.mobile_phone",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-operator",
              "label": "t:global.icons.operator",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-phone",
              "label": "t:global.icons.phone",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-send",
              "label": "t:global.icons.send",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-user",
              "label": "t:global.icons.user",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-burger",
              "label": "t:global.icons.burger",
              "group": "t:global.icons.food_category"
            },
            {
              "value": "picto-beer",
              "label": "t:global.icons.beer",
              "group": "t:global.icons.food_category"
            },
            {
              "value": "picto-coffee",
              "label": "t:global.icons.coffee",
              "group": "t:global.icons.food_category"
            },
            {
              "value": "picto-pizza",
              "label": "t:global.icons.pizza",
              "group": "t:global.icons.food_category"
            },
            {
              "value": "picto-bottle",
              "label": "t:global.icons.bottle",
              "group": "t:global.icons.food_category"
            },
            {
              "value": "picto-document",
              "label": "t:global.icons.document",
              "group": "t:global.icons.other_category"
            },
            {
              "value": "picto-error",
              "label": "t:global.icons.error",
              "group": "t:global.icons.other_category"
            },
            {
              "value": "picto-file",
              "label": "t:global.icons.file",
              "group": "t:global.icons.other_category"
            },
            {
              "value": "picto-jewelry",
              "label": "t:global.icons.jewelry",
              "group": "t:global.icons.other_category"
            },
            {
              "value": "picto-mask",
              "label": "t:global.icons.mask",
              "group": "t:global.icons.other_category"
            },
            {
              "value": "picto-music",
              "label": "t:global.icons.music",
              "group": "t:global.icons.other_category"
            },
            {
              "value": "picto-not-allowed",
              "label": "t:global.icons.not_allowed",
              "group": "t:global.icons.other_category"
            },
            {
              "value": "picto-target",
              "label": "t:global.icons.target",
              "group": "t:global.icons.other_category"
            },
            {
              "value": "picto-timer",
              "label": "t:global.icons.timer",
              "group": "t:global.icons.other_category"
            },
            {
              "value": "picto-success",
              "label": "t:global.icons.success",
              "group": "t:global.icons.other_category"
            }
          ]
        },
        {
          "type": "image_picker",
          "id": "custom_icon",
          "label": "t:global.icons.custom_icon",
          "info": "t:global.icons.custom_icon_info"
        },
        {
          "type": "range",
          "id": "icon_width",
          "min": 16,
          "max": 64,
          "step": 4,
          "unit": "px",
          "label": "t:global.icons.icon_width",
          "default": 24
        },
        {
          "type": "range",
          "id": "mobile_icon_width",
          "min": 16,
          "max": 64,
          "step": 4,
          "unit": "px",
          "label": "t:global.icons.mobile_icon_width",
          "default": 24
        },
        {
          "type": "inline_richtext",
          "id": "title",
          "label": "t:global.text.heading",
          "default": "Heading"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:global.text.content",
          "default": "<p>Short content about your store</p>"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.text_with_icons.presets.text_with_icons.name",
      "blocks": [
        {
          "type": "item",
          "settings": {
            "icon": "picto-delivery-truck",
            "title": "Free shipping",
            "content": "<p>Free worldwide shipping and returns - customs and duties taxes included</p>"
          }
        },
        {
          "type": "item",
          "settings": {
            "icon": "picto-customer-support",
            "title": "Customer service",
            "content": "<p>We are available from monday to friday to answer your questions.</p>"
          }
        },
        {
          "type": "item",
          "settings": {
            "icon": "picto-shield",
            "title": "Secure payment",
            "content": "<p>Your payment information is processed securely.</p>"
          }
        }
      ]
    }
  ]
}
{% endschema %}