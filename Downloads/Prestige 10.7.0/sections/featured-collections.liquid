{%- if section.blocks.size > 0 -%}
  <style>
    #shopify-section-{{ section.id }} {
      --product-list-items-per-row: {{ section.settings.products_per_row_mobile | times: 1 }};
      --product-list-horizontal-spacing-factor: {{ section.settings.horizontal_spacing_factor }};
      --product-list-vertical-spacing-factor: {{ section.settings.vertical_spacing_factor }};
    }

    @media screen and (min-width: 700px) {
      #shopify-section-{{ section.id }} {
        --product-list-items-per-row: {{ section.settings.products_per_row_desktop }};
      }
    }
  </style>

  {%- assign color_scheme_hash = section.settings.color_scheme.settings.background_gradient | default: section.settings.color_scheme.settings.background | md5 -%}

  <div class="section-spacing color-scheme color-scheme--{{ section.settings.color_scheme.id }} color-scheme--bg-{{ color_scheme_hash }} {% if section.settings.separate_section_with_border %}bordered-section{% endif %}">
    <div class="container">
      {%- assign wrapper_carousel_id = 'featured-collections-carousel-' | append: section.id -%}

      <div class="section-stack">
        <div class="v-stack justify-self-center gap-4 text-center sm:gap-5">
          {%- if section.settings.subheading != blank -%}
            <p class="h6 text-center">{{- section.settings.subheading -}}</p>
          {%- endif -%}

          {%- if section.blocks.size > 1 -%}
            <carousel-navigation align-selected class="featured-collections-navigation scroll-area bleed" aria-controls="{{ wrapper_carousel_id }}">
              {%- for block in section.blocks -%}
                <button class="h2" aria-current="{% if forloop.first %}true{% else %}false{% endif %}">
                  {{- block.settings.title | default: block.settings.collection.title | default: 'Collection' -}}
                </button>
              {%- endfor -%}
            </carousel-navigation>
          {%- else -%}
            <h2 class="h2">{{ section.blocks.first.settings.title | default: section.blocks.first.settings.collection.title | default: 'Collection' }}</h2>
          {%- endif -%}
        </div>

        <featured-collections-carousel id="{{ wrapper_carousel_id }}" class="featured-collections-carousel">
          {%- for block in section.blocks -%}
            <div class="featured-collections-carousel__item {% if forloop.first %}is-selected{% endif %}" {{ block.shopify_attributes }}>
              {%- capture product_list -%}
                {%- assign should_reveal = false -%}

                {%- if settings.stagger_products_apparition and forloop.first -%}
                  {%- assign should_reveal = true -%}
                {%- endif -%}

                {%- for product in block.settings.collection.products limit: block.settings.products_count -%}
                  {%- render 'product-card', product: product, reveal: should_reveal, position: forloop.index, hide_product_information: section.settings.hide_product_information, block: block -%}
                {%- else -%}
                  {%- for i in (1..block.settings.products_count) -%}
                    {%- render 'product-card-placeholder', reveal: should_reveal, loop_index: forloop.index0, hide_product_information: section.settings.hide_product_information -%}
                  {%- endfor -%}
                {%- endfor -%}
              {%- endcapture -%}

              {%- if section.settings.stack_products -%}
                <product-list class="product-list {% if section.settings.hide_product_information %}product-list--compact{% endif %} justify-center">
                  {{- product_list -}}
                </product-list>
              {%- else -%}
                {%- assign product_carousel_id = 'featured-collections-product-list-carousel-' | append: block.id -%}

                <product-list class="floating-controls-container floating-controls-container--inside floating-controls-container--on-hover">
                  <carousel-prev-button class="floating-controls-container__control" aria-controls="{{ product_carousel_id }}">
                    <button type="button" class="prev-next-button prev-next-button--prev circle-button hover:animate-icon-inline" disabled>
                      <span class="sr-only">{{ 'general.accessibility.previous' | t }}</span>
                      {%- render 'icon' with 'arrow-left', direction_aware: true -%}
                    </button>
                  </carousel-prev-button>

                  <scroll-carousel id="{{ product_carousel_id }}" group-cells allow-drag class="product-list {% if section.settings.hide_product_information %}product-list--compact{% endif %} product-list--carousel scroll-area bleed md:unbleed">
                    {{- product_list -}}
                  </scroll-carousel>
                  
                  <carousel-next-button class="floating-controls-container__control" aria-controls="{{ product_carousel_id }}">
                    <button type="button" class="prev-next-button prev-next-button--next circle-button hover:animate-icon-inline">
                      <span class="sr-only">{{ 'general.accessibility.next' | t }}</span>
                      {%- render 'icon' with 'arrow-right', direction_aware: true -%}
                    </button>
                  </carousel-next-button>
                </product-list>
              {%- endif -%}

              {%- if block.settings.link_text != blank -%}
                {%- assign button_link = block.settings.link_url | default: block.settings.collection.url | default: '#' -%}

                <div class="justify-self-center">
                  {%- render 'button', href: button_link, content: block.settings.link_text -%}
                </div>
              {%- endif -%}
            </div>
          {%- endfor -%}
        </featured-collections-carousel>
      </div>
    </div>
  </div>
{%- endif -%}

{% schema %}
{
  "name": "t:sections.featured_collections.name",
  "class": "shopify-section--featured-collections",
  "tag": "section",
  "disabled_on": {
    "templates": ["password"],
    "groups": ["header", "custom.overlay"]
  },
  "max_blocks": 5,
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:global.colors.scheme",
      "default": "scheme-1"
    },
    {
      "type": "checkbox",
      "id": "separate_section_with_border",
      "label": "t:global.section.separate_section_with_border",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "hide_product_information",
      "label": "t:global.product_list.hide_product_information",
      "info": "t:global.product_list.hide_product_information_info"
    },
    {
      "type": "header",
      "content": "t:global.product_list.product_list_category"
    },
    {
      "type": "checkbox",
      "id": "stack_products",
      "label": "t:global.product_list.stack_products",
      "default": false
    },
    {
      "type": "select",
      "id": "products_per_row_mobile",
      "label": "t:global.product_list.products_per_row_mobile",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ],
      "default": "2"
    },
    {
      "type": "range",
      "id": "products_per_row_desktop",
      "min": 2,
      "max": 6,
      "label": "t:global.product_list.products_per_row_desktop",
      "info": "t:global.product_list.products_per_row_desktop_info",
      "default": 4
    },
    {
      "type": "header",
      "content": "t:global.product_list.spacing_category",
      "info": "t:global.product_list.spacing_category_info"
    },
    {
      "type": "range",
      "min": 0.2,
      "max": 2,
      "step": 0.1,
      "id": "horizontal_spacing_factor",
      "label": "Horizontal spacing factor",
      "default": 1
    },
    {
      "type": "range",
      "min": 0.2,
      "max": 2,
      "step": 0.1,
      "id": "vertical_spacing_factor",
      "label": "Vertical spacing factor",
      "default": 1
    },
    {
      "type": "header",
      "content": "t:global.section.header_category"
    },
    {
      "type": "inline_richtext",
      "id": "subheading",
      "label": "t:global.text.subheading",
      "default": "Featured collection"
    }
  ],
  "blocks": [
    {
      "type": "collection",
      "name": "t:sections.featured_collections.blocks.collection.name",
      "settings": [
        {
          "type": "collection",
          "id": "collection",
          "label": "t:sections.featured_collections.blocks.collection.collection"
        },
        {
          "type": "range",
          "id": "products_count",
          "min": 2,
          "max": 50,
          "label": "t:global.product_list.products_to_show",
          "default": 8
        },
        {
          "type": "inline_richtext",
          "id": "title",
          "label": "t:global.text.heading",
          "info": "t:sections.featured_collections.blocks.collection.title_info"
        },
        {
          "type": "url",
          "id": "link_url",
          "label": "t:global.text.link_url",
          "info": "t:sections.featured_collections.blocks.collection.default_link_url"
        },
        {
          "type": "text",
          "id": "link_text",
          "label": "t:global.text.link_text",
          "default": "View all"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.featured_collections.presets.featured_collections.name",
      "blocks": [
        {
          "type": "collection"
        }
      ]
    }
  ]
}
{% endschema %}
