{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
HAINT-DEVELOPER NOTE: the slideshow is one of Prestige most complex sections due to the large number of settings it offers. We
highly recommend you to not edit the code unless you have fully reviewed and understood all the code. A simple change
may have important consequence on the section itself.
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
CSS
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

<style>
  #shopify-section-{{ section.id }} {
    {%- if section.settings.overlay_color != 'rgba(0,0,0,0)' -%}
      --content-over-media-overlay: {{ section.settings.overlay_color.rgb }} / {{ section.settings.overlay_opacity | divided_by: 100.0 }};
    {%- endif -%}

    {%- unless section.settings.image -%}
      --content-over-media-row-gap: 0px;
    {%- endunless -%}
  }
</style>

{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
LIQUID
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

{%- assign color_scheme_hash = section.settings.color_scheme.settings.background_gradient | default: section.settings.color_scheme.settings.background | md5 -%}

<div class="color-scheme color-scheme--{{ section.settings.color_scheme.id }} color-scheme--bg-{{ color_scheme_hash }} {% if section.settings.image != blank %}color-scheme--with-image-overlay{% else %}section-spacing {% if section.settings.separate_section_with_border %}bordered-section{% endif %}{% endif %}">
  <div class="newsletter content-over-media content-over-media--{{ section.settings.image_size }}">
    {%- if section.settings.image != blank -%}
      <picture>
        {%- if section.settings.mobile_image != blank -%}
          <source
              media="(max-width: 699px)"
              srcset="{{ section.settings.mobile_image | image_url: width: 400 }} 400w, {{ section.settings.mobile_image | image_url: width: 600 }} 600w, {{ section.settings.mobile_image | image_url: width: 800 }} 800w, {{ section.settings.mobile_image | image_url: width: 1000 }} 1000w"
              width="{{ section.settings.mobile_image.width }}"
              height="{{ section.settings.mobile_image.height }}"
          >
        {%- endif -%}

        {{- section.settings.image | image_url: width: section.settings.image.width | image_tag: sizes: '100vw', widths: '200,300,400,500,600,700,800,900,1000,1200,1400,1600,1800,2000,2200,2400,2600,2800,3000,3200' -}}
      </picture>
    {%- endif -%}

    <div class="content section-stack gap-8 text-center">
      {%- render 'section-header', subheading: section.settings.subheading, heading: section.settings.title, content: section.settings.content -%}

      {%- assign newsletter_form_id = 'newsletter-form-' | append: section.id -%}

      {%- form 'customer', id: newsletter_form_id, class: 'form justify-self-center' -%}
        {%- if form.posted_successfully? -%}
          {%- assign success_message = 'general.newsletter.subscribed_successfully' | t -%}
          {%- render 'banner', content: success_message, status: 'success', text_alignment: 'center' -%}
        {%- else -%}
          {%- if form.errors -%}
            {%- capture error_message -%}{{ form.errors.translated_fields['email'] }} {{ form.errors.messages['email'] }}{%- endcapture -%}
            {%- render 'banner', content: error_message, status: 'error', text_alignment: 'center' -%}
          {%- endif -%}

          <input type="hidden" name="contact[tags]" value="newsletter">

          <div class="form-row">
            {%- assign input_label = 'general.newsletter.email' | t -%}

            {%- render 'input', name: 'contact[email]', label: input_label, label_hidden: true, type: 'email', required: true, autocomplete: 'email', enterkeyhint: 'send' -%}
            {%- render 'button', type: 'submit', content: section.settings.button_text -%}
          </div>
        {%- endif -%}
      {%- endform -%}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.newsletter.name",
  "class": "shopify-section--newsletter",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "custom.overlay"]
  },
  "settings": [
    {
      "type": "paragraph",
      "content": "t:sections.newsletter.instructions"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:global.colors.scheme",
      "default": "scheme-3"
    },
    {
      "type": "checkbox",
      "id": "separate_section_with_border",
      "label": "t:global.section.separate_section_with_border",
      "default": true
    },
    {
      "type": "select",
      "id": "image_size",
      "label": "t:global.image.size",
      "options": [
        {
          "value": "auto",
          "label": "t:global.sizes.original_image_ratio"
        },
        {
          "value": "sm",
          "label": "t:global.sizes.small"
        },
        {
          "value": "md",
          "label": "t:global.sizes.medium"
        },
        {
          "value": "lg",
          "label": "t:global.sizes.large"
        },
        {
          "value": "fill",
          "label": "t:global.sizes.fit_screen"
        }
      ],
      "info": "t:global.image.ratio_avoid_cropping_info",
      "default": "auto"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:global.image.image",
      "info": "t:sections.newsletter.image_size_recommendation"
    },
    {
      "type": "image_picker",
      "id": "mobile_image",
      "label": "t:global.image.mobile_image",
      "info": "t:sections.newsletter.mobile_image_size_recommendation"
    },
    {
      "type": "inline_richtext",
      "id": "subheading",
      "label": "t:global.text.subheading",
      "default": "Keep me updated"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "label": "t:global.text.heading",
      "default": "Newsletter"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "t:global.text.content",
      "default": "<p>A short sentence describing what someone will receive by subscribing.</p>"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "t:global.text.button_text",
      "default": "Subscribe"
    },
    {
      "type": "header",
      "content": "t:global.colors.category"
    },
    {
      "type": "color",
      "id": "overlay_color",
      "label": "t:global.colors.overlay_color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "label": "t:global.colors.overlay_opacity",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:sections.newsletter.presets.newsletter.name",
      "settings": {}
    }
  ]
}
{% endschema %}

{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
HAINT-DEVELOPER NOTE: the slideshow is one of Prestige most complex sections due to the large number of settings it offers. We
highly recommend you to not edit the code unless you have fully reviewed and understood all the code. A simple change
may have important consequence on the section itself.
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}
