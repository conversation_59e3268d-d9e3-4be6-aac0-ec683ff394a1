<div class="section-spacing section-spacing--tight">
  <div class="container container--lg">
    {%- if customer.addresses_count == 0 -%}
      <div class="empty-state">
        <div class="prose">
          <h1 class="h4">{{ 'customer.addresses.title' | t }}</h1>
          <p>{{ 'customer.addresses.no_addresses' | t }}</p>
          <button class="button" type="button" aria-controls="customer-address-new">{{ 'customer.addresses.add_address' | t }}</button>
        </div>
      </div>
    {%- else -%}
      <div class="section-stack">
        <div class="v-stack justify-items-start gap-6">
          <a href="{{ routes.account_url }}" class="text-with-icon h6 link-faded">
            {%- render 'icon' with 'chevron-left', width: 10, direction_aware: true -%}
            {{- 'customer.order.back' | t: name: order.name -}}
          </a>

          <div class="section-header text-start">
            <div class="prose">
              <h1 class="h3">{{ 'customer.addresses.title' | t }}</h1>
              <button class="button" type="button" aria-controls="customer-address-new">{{ 'customer.addresses.add_address' | t }}</button>
            </div>
          </div>
        </div>

        {%- paginate customer.addresses by 16 -%}
          {%- if customer.addresses_count == 0 -%}
            <div class="empty-state">
              <p>{{ 'customer.addresses.no_addresses' | t }}</p>

              {%- assign button_content = 'customer.addresses.add_address' | t -%}
              {%- render 'button', content: button_content, aria_controls: 'customer-address-new' -%}
            </div>
          {%- else -%}
            <div class="v-stack gap-8 sm:gap-12">
              <div class="customer-address-list">
                {%- for address in customer.addresses -%}
                  <div class="v-stack gap-6 sm:gap-8">
                    {%- if address == customer.default_address -%}
                      <p class="customer-account-category h6 text-subdued">{{ 'customer.addresses.default_address' | t }}</p>
                    {%- else -%}
                      <p class="customer-account-category h6 text-subdued">{{ 'customer.addresses.address_title' | t: position: forloop.index }}</p>
                    {%- endif -%}

                    <div class="v-stack gap-4">
                      {{- address | format_address -}}

                      <div class="h-stack gap-4">
                        <button type="button" class="link" aria-controls="customer-address-{{ address.id }}">{{ 'customer.addresses.edit' | t }}</button>

                        <form method="post" action="{{ address.url }}">
                          <input type="hidden" name="_method" value="delete">

                          <confirm-button data-message="{{ 'customer.addresses.delete_confirm' | t | escape }}">
                            <button type="submit" class="link">{{ 'customer.addresses.delete' | t }}</button>
                          </confirm-button>
                        </form>
                      </div>
                    </div>
                  </div>
                {%- endfor -%}
              </div>

              {%- render 'pagination', paginate: paginate -%}
            </div>
          {%- endif -%}
        {%- endpaginate -%}
      </div>
    {%- endif -%}

    {%- comment -%}
    --------------------------------------------------------------------------------------------------------------------
    GENERATE THE FORMS
    --------------------------------------------------------------------------------------------------------------------
    {%- endcomment -%}

    {%- render 'address-form', address: customer.new_address -%}

    {%- for address in customer.addresses -%}
      {%- render 'address-form', address: address -%}
    {%- endfor -%}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.main_customers_addresses.name",
  "class": "shopify-section--main-customers-addresses",
  "tag": "section"
}
{% endschema %}