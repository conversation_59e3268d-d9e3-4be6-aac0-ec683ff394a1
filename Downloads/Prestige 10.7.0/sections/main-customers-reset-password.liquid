<div class="section-spacing section-spacing--tight">
  <div class="container container--xxs">
    <div class="customer-account-box">
      <div class="v-stack gap-6">
        <div class="v-stack gap-4">
          <h1 class="h3">{{- 'customer.reset_password.title' | t -}}</h1>
          <p>{{ 'customer.reset_password.instructions' | t }}</p>
        </div>

        {%- form 'reset_customer_password', class: 'form' -%}
          <div class="fieldset">
            {%- if form.errors -%}
              {%- assign form_errors = form.errors | default_errors -%}
              {%- render 'banner', status: 'error', content: form_errors -%}
            {%- endif -%}

            {%- assign password_label = 'customer.reset_password.password' | t -%}
            {%- render 'input', type: 'password', name: 'customer[password]', label: password_label, autocomplete: 'new-password', required: true -%}

            {%- assign password_confirmation_label = 'customer.reset_password.password_confirmation' | t -%}
            {%- render 'input', type: 'password', name: 'customer[password_confirmation]', label: password_confirmation_label, autocomplete: 'new-password', required: true -%}
          </div>

          {%- assign submit_label = 'customer.reset_password.submit' | t -%}
          {%- render 'button', content: submit_label, type: 'submit' -%}
        {%- endform -%}
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.main_customers_reset_password.name",
  "class": "shopify-section--main-customers-reset-password",
  "tag": "section"
}
{% endschema %}