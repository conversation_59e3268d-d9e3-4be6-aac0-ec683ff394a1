var vr=Object.defineProperty;var gr=(t,e)=>{for(var r in e)vr(t,r,{get:e[r],enumerable:!0})};var Ct={};gr(Ct,{createFocusTrap:()=>Gr});var St=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"],De=St.join(","),Ot=typeof Element>"u",ie=Ot?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,Pe=!Ot&&Element.prototype.getRootNode?function(t){var e;return t==null||(e=t.getRootNode)===null||e===void 0?void 0:e.call(t)}:function(t){return t?.ownerDocument},Ne=function t(e,r){var i;r===void 0&&(r=!0);var n=e==null||(i=e.getAttribute)===null||i===void 0?void 0:i.call(e,"inert"),o=n===""||n==="true",s=o||r&&e&&t(e.parentNode);return s},yr=function(e){var r,i=e==null||(r=e.getAttribute)===null||r===void 0?void 0:r.call(e,"contenteditable");return i===""||i==="true"},_t=function(e,r,i){if(Ne(e))return[];var n=Array.prototype.slice.apply(e.querySelectorAll(De));return r&&ie.call(e,De)&&n.unshift(e),n=n.filter(i),n},Tt=function t(e,r,i){for(var n=[],o=Array.from(e);o.length;){var s=o.shift();if(!Ne(s,!1))if(s.tagName==="SLOT"){var l=s.assignedElements(),u=l.length?l:s.children,f=t(u,!0,i);i.flatten?n.push.apply(n,f):n.push({scopeParent:s,candidates:f})}else{var m=ie.call(s,De);m&&i.filter(s)&&(r||!e.includes(s))&&n.push(s);var p=s.shadowRoot||typeof i.getShadowRoot=="function"&&i.getShadowRoot(s),d=!Ne(p,!1)&&(!i.shadowRootFilter||i.shadowRootFilter(s));if(p&&d){var O=t(p===!0?s.children:p.children,!0,i);i.flatten?n.push.apply(n,O):n.push({scopeParent:s,candidates:O})}else o.unshift.apply(o,s.children)}}return n},At=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},Z=function(e){if(!e)throw new Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||yr(e))&&!At(e)?0:e.tabIndex},br=function(e,r){var i=Z(e);return i<0&&r&&!At(e)?0:i},wr=function(e,r){return e.tabIndex===r.tabIndex?e.documentOrder-r.documentOrder:e.tabIndex-r.tabIndex},Ft=function(e){return e.tagName==="INPUT"},Er=function(e){return Ft(e)&&e.type==="hidden"},xr=function(e){var r=e.tagName==="DETAILS"&&Array.prototype.slice.apply(e.children).some(function(i){return i.tagName==="SUMMARY"});return r},Sr=function(e,r){for(var i=0;i<e.length;i++)if(e[i].checked&&e[i].form===r)return e[i]},Or=function(e){if(!e.name)return!0;var r=e.form||Pe(e),i=function(l){return r.querySelectorAll('input[type="radio"][name="'+l+'"]')},n;if(typeof window<"u"&&typeof window.CSS<"u"&&typeof window.CSS.escape=="function")n=i(window.CSS.escape(e.name));else try{n=i(e.name)}catch(s){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",s.message),!1}var o=Sr(n,e.form);return!o||o===e},_r=function(e){return Ft(e)&&e.type==="radio"},Tr=function(e){return _r(e)&&!Or(e)},Ar=function(e){var r,i=e&&Pe(e),n=(r=i)===null||r===void 0?void 0:r.host,o=!1;if(i&&i!==e){var s,l,u;for(o=!!((s=n)!==null&&s!==void 0&&(l=s.ownerDocument)!==null&&l!==void 0&&l.contains(n)||e!=null&&(u=e.ownerDocument)!==null&&u!==void 0&&u.contains(e));!o&&n;){var f,m,p;i=Pe(n),n=(f=i)===null||f===void 0?void 0:f.host,o=!!((m=n)!==null&&m!==void 0&&(p=m.ownerDocument)!==null&&p!==void 0&&p.contains(n))}}return o},xt=function(e){var r=e.getBoundingClientRect(),i=r.width,n=r.height;return i===0&&n===0},Fr=function(e,r){var i=r.displayCheck,n=r.getShadowRoot;if(getComputedStyle(e).visibility==="hidden")return!0;var o=ie.call(e,"details>summary:first-of-type"),s=o?e.parentElement:e;if(ie.call(s,"details:not([open]) *"))return!0;if(!i||i==="full"||i==="legacy-full"){if(typeof n=="function"){for(var l=e;e;){var u=e.parentElement,f=Pe(e);if(u&&!u.shadowRoot&&n(u)===!0)return xt(e);e.assignedSlot?e=e.assignedSlot:!u&&f!==e.ownerDocument?e=f.host:e=u}e=l}if(Ar(e))return!e.getClientRects().length;if(i!=="legacy-full")return!0}else if(i==="non-zero-area")return xt(e);return!1},Ir=function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var r=e.parentElement;r;){if(r.tagName==="FIELDSET"&&r.disabled){for(var i=0;i<r.children.length;i++){var n=r.children.item(i);if(n.tagName==="LEGEND")return ie.call(r,"fieldset[disabled] *")?!0:!n.contains(e)}return!0}r=r.parentElement}return!1},Le=function(e,r){return!(r.disabled||Ne(r)||Er(r)||Fr(r,e)||xr(r)||Ir(r))},Qe=function(e,r){return!(Tr(r)||Z(r)<0||!Le(e,r))},Dr=function(e){var r=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(r)||r>=0)},Pr=function t(e){var r=[],i=[];return e.forEach(function(n,o){var s=!!n.scopeParent,l=s?n.scopeParent:n,u=br(l,s),f=s?t(n.candidates):l;u===0?s?r.push.apply(r,f):r.push(l):i.push({documentOrder:o,tabIndex:u,item:n,isScope:s,content:f})}),i.sort(wr).reduce(function(n,o){return o.isScope?n.push.apply(n,o.content):n.push(o.content),n},[]).concat(r)},It=function(e,r){r=r||{};var i;return r.getShadowRoot?i=Tt([e],r.includeContainer,{filter:Qe.bind(null,r),flatten:!1,getShadowRoot:r.getShadowRoot,shadowRootFilter:Dr}):i=_t(e,r.includeContainer,Qe.bind(null,r)),Pr(i)},Dt=function(e,r){r=r||{};var i;return r.getShadowRoot?i=Tt([e],r.includeContainer,{filter:Le.bind(null,r),flatten:!0,getShadowRoot:r.getShadowRoot}):i=_t(e,r.includeContainer,Le.bind(null,r)),i},ne=function(e,r){if(r=r||{},!e)throw new Error("No node provided");return ie.call(e,De)===!1?!1:Qe(r,e)},Nr=St.concat("iframe").join(","),Re=function(e,r){if(r=r||{},!e)throw new Error("No node provided");return ie.call(e,Nr)===!1?!1:Le(r,e)};function et(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,i=Array(e);r<e;r++)i[r]=t[r];return i}function Lr(t){if(Array.isArray(t))return et(t)}function Rr(t,e,r){return(e=jr(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Cr(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Mr(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Pt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})),r.push.apply(r,i)}return r}function Nt(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Pt(Object(r),!0).forEach(function(i){Rr(t,i,r[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Pt(Object(r)).forEach(function(i){Object.defineProperty(t,i,Object.getOwnPropertyDescriptor(r,i))})}return t}function kr(t){return Lr(t)||Cr(t)||$r(t)||Mr()}function zr(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var i=r.call(t,e||"default");if(typeof i!="object")return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function jr(t){var e=zr(t,"string");return typeof e=="symbol"?e:e+""}function $r(t,e){if(t){if(typeof t=="string")return et(t,e);var r={}.toString.call(t).slice(8,-1);return r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set"?Array.from(t):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?et(t,e):void 0}}var Lt={activateTrap:function(e,r){if(e.length>0){var i=e[e.length-1];i!==r&&i._setPausedState(!0)}var n=e.indexOf(r);n===-1||e.splice(n,1),e.push(r)},deactivateTrap:function(e,r){var i=e.indexOf(r);i!==-1&&e.splice(i,1),e.length>0&&!e[e.length-1]._isManuallyPaused()&&e[e.length-1]._setPausedState(!1)}},Br=function(e){return e.tagName&&e.tagName.toLowerCase()==="input"&&typeof e.select=="function"},Wr=function(e){return e?.key==="Escape"||e?.key==="Esc"||e?.keyCode===27},pe=function(e){return e?.key==="Tab"||e?.keyCode===9},Vr=function(e){return pe(e)&&!e.shiftKey},Kr=function(e){return pe(e)&&e.shiftKey},Rt=function(e){return setTimeout(e,0)},he=function(e){for(var r=arguments.length,i=new Array(r>1?r-1:0),n=1;n<r;n++)i[n-1]=arguments[n];return typeof e=="function"?e.apply(void 0,i):e},Ce=function(e){return e.target.shadowRoot&&typeof e.composedPath=="function"?e.composedPath()[0]:e.target},Hr=[],Gr=function(e,r){var i=r?.document||document,n=r?.trapStack||Hr,o=Nt({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0,isKeyForward:Vr,isKeyBackward:Kr},r),s={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,manuallyPaused:!1,delayInitialFocusTimer:void 0,recentNavEvent:void 0},l,u=function(a,c,h){return a&&a[c]!==void 0?a[c]:o[h||c]},f=function(a,c){var h=typeof c?.composedPath=="function"?c.composedPath():void 0;return s.containerGroups.findIndex(function(g){var y=g.container,S=g.tabbableNodes;return y.contains(a)||h?.includes(y)||S.find(function(b){return b===a})})},m=function(a){var c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},h=c.hasFallback,g=h===void 0?!1:h,y=c.params,S=y===void 0?[]:y,b=o[a];if(typeof b=="function"&&(b=b.apply(void 0,kr(S))),b===!0&&(b=void 0),!b){if(b===void 0||b===!1)return b;throw new Error("`".concat(a,"` was specified but was not a node, or did not return a node"))}var I=b;if(typeof b=="string"){try{I=i.querySelector(b)}catch(P){throw new Error("`".concat(a,'` appears to be an invalid selector; error="').concat(P.message,'"'))}if(!I&&!g)throw new Error("`".concat(a,"` as selector refers to no known node"))}return I},p=function(){var a=m("initialFocus",{hasFallback:!0});if(a===!1)return!1;if(a===void 0||a&&!Re(a,o.tabbableOptions))if(f(i.activeElement)>=0)a=i.activeElement;else{var c=s.tabbableGroups[0],h=c&&c.firstTabbableNode;a=h||m("fallbackFocus")}else a===null&&(a=m("fallbackFocus"));if(!a)throw new Error("Your focus-trap needs to have at least one focusable element");return a},d=function(){if(s.containerGroups=s.containers.map(function(a){var c=It(a,o.tabbableOptions),h=Dt(a,o.tabbableOptions),g=c.length>0?c[0]:void 0,y=c.length>0?c[c.length-1]:void 0,S=h.find(function(P){return ne(P)}),b=h.slice().reverse().find(function(P){return ne(P)}),I=!!c.find(function(P){return Z(P)>0});return{container:a,tabbableNodes:c,focusableNodes:h,posTabIndexesFound:I,firstTabbableNode:g,lastTabbableNode:y,firstDomTabbableNode:S,lastDomTabbableNode:b,nextTabbableNode:function(re){var ue=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,q=c.indexOf(re);return q<0?ue?h.slice(h.indexOf(re)+1).find(function(de){return ne(de)}):h.slice(0,h.indexOf(re)).reverse().find(function(de){return ne(de)}):c[q+(ue?1:-1)]}}}),s.tabbableGroups=s.containerGroups.filter(function(a){return a.tabbableNodes.length>0}),s.tabbableGroups.length<=0&&!m("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times");if(s.containerGroups.find(function(a){return a.posTabIndexesFound})&&s.containerGroups.length>1)throw new Error("At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.")},O=function(a){var c=a.activeElement;if(c)return c.shadowRoot&&c.shadowRoot.activeElement!==null?O(c.shadowRoot):c},E=function(a){if(a!==!1&&a!==O(document)){if(!a||!a.focus){E(p());return}a.focus({preventScroll:!!o.preventScroll}),s.mostRecentlyFocusedNode=a,Br(a)&&a.select()}},T=function(a){var c=m("setReturnFocus",{params:[a]});return c||(c===!1?!1:a)},F=function(a){var c=a.target,h=a.event,g=a.isBackward,y=g===void 0?!1:g;c=c||Ce(h),d();var S=null;if(s.tabbableGroups.length>0){var b=f(c,h),I=b>=0?s.containerGroups[b]:void 0;if(b<0)y?S=s.tabbableGroups[s.tabbableGroups.length-1].lastTabbableNode:S=s.tabbableGroups[0].firstTabbableNode;else if(y){var P=s.tabbableGroups.findIndex(function(Ye){var Je=Ye.firstTabbableNode;return c===Je});if(P<0&&(I.container===c||Re(c,o.tabbableOptions)&&!ne(c,o.tabbableOptions)&&!I.nextTabbableNode(c,!1))&&(P=b),P>=0){var re=P===0?s.tabbableGroups.length-1:P-1,ue=s.tabbableGroups[re];S=Z(c)>=0?ue.lastTabbableNode:ue.lastDomTabbableNode}else pe(h)||(S=I.nextTabbableNode(c,!1))}else{var q=s.tabbableGroups.findIndex(function(Ye){var Je=Ye.lastTabbableNode;return c===Je});if(q<0&&(I.container===c||Re(c,o.tabbableOptions)&&!ne(c,o.tabbableOptions)&&!I.nextTabbableNode(c))&&(q=b),q>=0){var de=q===s.tabbableGroups.length-1?0:q+1,Et=s.tabbableGroups[de];S=Z(c)>=0?Et.firstTabbableNode:Et.firstDomTabbableNode}else pe(h)||(S=I.nextTabbableNode(c))}}else S=m("fallbackFocus");return S},A=function(a){var c=Ce(a);if(!(f(c,a)>=0)){if(he(o.clickOutsideDeactivates,a)){l.deactivate({returnFocus:o.returnFocusOnDeactivate});return}he(o.allowOutsideClick,a)||a.preventDefault()}},M=function(a){var c=Ce(a),h=f(c,a)>=0;if(h||c instanceof Document)h&&(s.mostRecentlyFocusedNode=c);else{a.stopImmediatePropagation();var g,y=!0;if(s.mostRecentlyFocusedNode)if(Z(s.mostRecentlyFocusedNode)>0){var S=f(s.mostRecentlyFocusedNode),b=s.containerGroups[S].tabbableNodes;if(b.length>0){var I=b.findIndex(function(P){return P===s.mostRecentlyFocusedNode});I>=0&&(o.isKeyForward(s.recentNavEvent)?I+1<b.length&&(g=b[I+1],y=!1):I-1>=0&&(g=b[I-1],y=!1))}}else s.containerGroups.some(function(P){return P.tabbableNodes.some(function(re){return Z(re)>0})})||(y=!1);else y=!1;y&&(g=F({target:s.mostRecentlyFocusedNode,isBackward:o.isKeyBackward(s.recentNavEvent)})),E(g||s.mostRecentlyFocusedNode||p())}s.recentNavEvent=void 0},k=function(a){var c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;s.recentNavEvent=a;var h=F({event:a,isBackward:c});h&&(pe(a)&&a.preventDefault(),E(h))},j=function(a){(o.isKeyForward(a)||o.isKeyBackward(a))&&k(a,o.isKeyBackward(a))},D=function(a){Wr(a)&&he(o.escapeDeactivates,a)!==!1&&(a.preventDefault(),l.deactivate())},z=function(a){var c=Ce(a);f(c,a)>=0||he(o.clickOutsideDeactivates,a)||he(o.allowOutsideClick,a)||(a.preventDefault(),a.stopImmediatePropagation())},$=function(){if(s.active)return Lt.activateTrap(n,l),s.delayInitialFocusTimer=o.delayInitialFocus?Rt(function(){E(p())}):E(p()),i.addEventListener("focusin",M,!0),i.addEventListener("mousedown",A,{capture:!0,passive:!1}),i.addEventListener("touchstart",A,{capture:!0,passive:!1}),i.addEventListener("click",z,{capture:!0,passive:!1}),i.addEventListener("keydown",j,{capture:!0,passive:!1}),i.addEventListener("keydown",D),l},L=function(){if(s.active)return i.removeEventListener("focusin",M,!0),i.removeEventListener("mousedown",A,!0),i.removeEventListener("touchstart",A,!0),i.removeEventListener("click",z,!0),i.removeEventListener("keydown",j,!0),i.removeEventListener("keydown",D),l},x=function(a){var c=a.some(function(h){var g=Array.from(h.removedNodes);return g.some(function(y){return y===s.mostRecentlyFocusedNode})});c&&E(p())},W=typeof window<"u"&&"MutationObserver"in window?new MutationObserver(x):void 0,w=function(){W&&(W.disconnect(),s.active&&!s.paused&&s.containers.map(function(a){W.observe(a,{subtree:!0,childList:!0})}))};return l={get active(){return s.active},get paused(){return s.paused},activate:function(a){if(s.active)return this;var c=u(a,"onActivate"),h=u(a,"onPostActivate"),g=u(a,"checkCanFocusTrap");g||d(),s.active=!0,s.paused=!1,s.nodeFocusedBeforeActivation=i.activeElement,c?.();var y=function(){g&&d(),$(),w(),h?.()};return g?(g(s.containers.concat()).then(y,y),this):(y(),this)},deactivate:function(a){if(!s.active)return this;var c=Nt({onDeactivate:o.onDeactivate,onPostDeactivate:o.onPostDeactivate,checkCanReturnFocus:o.checkCanReturnFocus},a);clearTimeout(s.delayInitialFocusTimer),s.delayInitialFocusTimer=void 0,L(),s.active=!1,s.paused=!1,w(),Lt.deactivateTrap(n,l);var h=u(c,"onDeactivate"),g=u(c,"onPostDeactivate"),y=u(c,"checkCanReturnFocus"),S=u(c,"returnFocus","returnFocusOnDeactivate");h?.();var b=function(){Rt(function(){S&&E(T(s.nodeFocusedBeforeActivation)),g?.()})};return S&&y?(y(T(s.nodeFocusedBeforeActivation)).then(b,b),this):(b(),this)},pause:function(a){return s.active?(s.manuallyPaused=!0,this._setPausedState(!0,a)):this},unpause:function(a){return s.active?(s.manuallyPaused=!1,n[n.length-1]!==this?this:this._setPausedState(!1,a)):this},updateContainerElements:function(a){var c=[].concat(a).filter(Boolean);return s.containers=c.map(function(h){return typeof h=="string"?i.querySelector(h):h}),s.active&&d(),w(),this}},Object.defineProperties(l,{_isManuallyPaused:{value:function(){return s.manuallyPaused}},_setPausedState:{value:function(a,c){if(s.paused===a)return this;if(s.paused=a,a){var h=u(c,"onPause"),g=u(c,"onPostPause");h?.(),L(),w(),g?.()}else{var y=u(c,"onUnpause"),S=u(c,"onPostUnpause");y?.(),d(),$(),w(),S?.()}return this}}}),l.updateContainerElements(e),l};function tt(t,e){t.indexOf(e)===-1&&t.push(e)}function rt(t,e){let r=t.indexOf(e);r>-1&&t.splice(r,1)}var me=(t,e,r)=>Math.min(Math.max(r,t),e);var _={duration:.3,delay:0,endDelay:0,repeat:0,easing:"ease"};var C=t=>typeof t=="number";var K=t=>Array.isArray(t)&&!C(t[0]);var Mt=(t,e,r)=>{let i=e-t;return((r-t)%i+i)%i+t};function ve(t,e){return K(t)?t[Mt(0,t.length,e)]:t}var se=(t,e,r)=>-r*t+r*e+t;var ge=()=>{},R=t=>t;var V=(t,e,r)=>e-t===0?1:(r-t)/(e-t);function ce(t,e){let r=t[t.length-1];for(let i=1;i<=e;i++){let n=V(0,e,i);t.push(se(r,1,n))}}function oe(t){let e=[0];return ce(e,t-1),e}function ye(t,e=oe(t.length),r=R){let i=t.length,n=i-e.length;return n>0&&ce(e,n),o=>{let s=0;for(;s<i-2&&!(o<e[s+1]);s++);let l=me(0,1,V(e[s],e[s+1],o));return l=ve(r,s)(l),se(t[s],t[s+1],l)}}var be=t=>Array.isArray(t)&&C(t[0]);var X=t=>typeof t=="object"&&!!t.createAnimation;var N=t=>typeof t=="function";var H=t=>typeof t=="string";var Y={ms:t=>t*1e3,s:t=>t/1e3};function it(t,e){return e?t*(1e3/e):0}var kt=(t,e,r)=>(((1-3*r+3*e)*t+(3*r-6*e))*t+3*e)*t,Ur=1e-7,qr=12;function Zr(t,e,r,i,n){let o,s,l=0;do s=e+(r-e)/2,o=kt(s,i,n)-t,o>0?r=s:e=s;while(Math.abs(o)>Ur&&++l<qr);return s}function ae(t,e,r,i){if(t===e&&r===i)return R;let n=o=>Zr(o,0,1,t,r);return o=>o===0||o===1?o:kt(n(o),e,i)}var nt=(t,e="end")=>r=>{r=e==="end"?Math.min(r,.999):Math.max(r,.001);let i=r*t,n=e==="end"?Math.floor(i):Math.ceil(i);return me(0,1,n/t)};var Xr={ease:ae(.25,.1,.25,1),"ease-in":ae(.42,0,1,1),"ease-in-out":ae(.42,0,.58,1),"ease-out":ae(0,0,.58,1)},Yr=/\((.*?)\)/;function fe(t){if(N(t))return t;if(be(t))return ae(...t);let e=Xr[t];if(e)return e;if(t.startsWith("steps")){let r=Yr.exec(t);if(r){let i=r[1].split(",");return nt(parseFloat(i[0]),i[1].trim())}}return R}var G=class{constructor(e,r=[0,1],{easing:i,duration:n=_.duration,delay:o=_.delay,endDelay:s=_.endDelay,repeat:l=_.repeat,offset:u,direction:f="normal",autoplay:m=!0}={}){if(this.startTime=null,this.rate=1,this.t=0,this.cancelTimestamp=null,this.easing=R,this.duration=0,this.totalDuration=0,this.repeat=0,this.playState="idle",this.finished=new Promise((d,O)=>{this.resolve=d,this.reject=O}),i=i||_.easing,X(i)){let d=i.createAnimation(r);i=d.easing,r=d.keyframes||r,n=d.duration||n}this.repeat=l,this.easing=K(i)?R:fe(i),this.updateDuration(n);let p=ye(r,u,K(i)?i.map(fe):R);this.tick=d=>{var O;o=o;let E=0;this.pauseTime!==void 0?E=this.pauseTime:E=(d-this.startTime)*this.rate,this.t=E,E/=1e3,E=Math.max(E-o,0),this.playState==="finished"&&this.pauseTime===void 0&&(E=this.totalDuration);let T=E/this.duration,F=Math.floor(T),A=T%1;!A&&T>=1&&(A=1),A===1&&F--;let M=F%2;(f==="reverse"||f==="alternate"&&M||f==="alternate-reverse"&&!M)&&(A=1-A);let k=E>=this.totalDuration?1:Math.min(A,1),j=p(this.easing(k));e(j),this.pauseTime===void 0&&(this.playState==="finished"||E>=this.totalDuration+s)?(this.playState="finished",(O=this.resolve)===null||O===void 0||O.call(this,j)):this.playState!=="idle"&&(this.frameRequestId=requestAnimationFrame(this.tick))},m&&this.play()}play(){let e=performance.now();this.playState="running",this.pauseTime!==void 0?this.startTime=e-this.pauseTime:this.startTime||(this.startTime=e),this.cancelTimestamp=this.startTime,this.pauseTime=void 0,this.frameRequestId=requestAnimationFrame(this.tick)}pause(){this.playState="paused",this.pauseTime=this.t}finish(){this.playState="finished",this.tick(0)}stop(){var e;this.playState="idle",this.frameRequestId!==void 0&&cancelAnimationFrame(this.frameRequestId),(e=this.reject)===null||e===void 0||e.call(this,!1)}cancel(){this.stop(),this.tick(this.cancelTimestamp)}reverse(){this.rate*=-1}commitStyles(){}updateDuration(e){this.duration=e,this.totalDuration=e*(this.repeat+1)}get currentTime(){return this.t}set currentTime(e){this.pauseTime!==void 0||this.rate===0?this.pauseTime=e:this.startTime=performance.now()-e/this.rate}get playbackRate(){return this.rate}set playbackRate(e){this.rate=e}};var we=function(){};var Ee=class{setAnimation(e){this.animation=e,e?.finished.then(()=>this.clearAnimation()).catch(()=>{})}clearAnimation(){this.animation=this.generator=void 0}};var st=new WeakMap;function Me(t){return st.has(t)||st.set(t,{transforms:[],values:new Map}),st.get(t)}function zt(t,e){return t.has(e)||t.set(e,new Ee),t.get(e)}var Jr=["","X","Y","Z"],Qr=["translate","scale","rotate","skew"],xe={x:"translateX",y:"translateY",z:"translateZ"},jt={syntax:"<angle>",initialValue:"0deg",toDefaultUnit:t=>t+"deg"},ei={translate:{syntax:"<length-percentage>",initialValue:"0px",toDefaultUnit:t=>t+"px"},rotate:jt,scale:{syntax:"<number>",initialValue:1,toDefaultUnit:R},skew:jt},J=new Map,ze=t=>`--motion-${t}`,ke=["x","y","z"];Qr.forEach(t=>{Jr.forEach(e=>{ke.push(t+e),J.set(ze(t+e),ei[t])})});var ti=(t,e)=>ke.indexOf(t)-ke.indexOf(e),ri=new Set(ke),je=t=>ri.has(t),$t=(t,e)=>{xe[e]&&(e=xe[e]);let{transforms:r}=Me(t);tt(r,e),t.style.transform=ii(r)},ii=t=>t.sort(ti).reduce(ni,"").trim(),ni=(t,e)=>`${t} ${e}(var(${ze(e)}))`;var Se=t=>t.startsWith("--"),Bt=new Set;function Wt(t){if(!Bt.has(t)){Bt.add(t);try{let{syntax:e,initialValue:r}=J.has(t)?J.get(t):{};CSS.registerProperty({name:t,inherits:!1,syntax:e,initialValue:r})}catch{}}}var ot=(t,e)=>document.createElement("div").animate(t,e),Vt={cssRegisterProperty:()=>typeof CSS<"u"&&Object.hasOwnProperty.call(CSS,"registerProperty"),waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate"),partialKeyframes:()=>{try{ot({opacity:[1]})}catch{return!1}return!0},finished:()=>!!ot({opacity:[0,1]},{duration:.001}).finished,linearEasing:()=>{try{ot({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0}},at={},Q={};for(let t in Vt)Q[t]=()=>(at[t]===void 0&&(at[t]=Vt[t]()),at[t]);var si=.015,oi=(t,e)=>{let r="",i=Math.round(e/si);for(let n=0;n<i;n++)r+=t(V(0,i-1,n))+", ";return r.substring(0,r.length-2)},lt=(t,e)=>N(t)?Q.linearEasing()?`linear(${oi(t,e)})`:_.easing:be(t)?ai(t):t,ai=([t,e,r,i])=>`cubic-bezier(${t}, ${e}, ${r}, ${i})`;function Kt(t,e){for(let r=0;r<t.length;r++)t[r]===null&&(t[r]=r?t[r-1]:e());return t}var $e=t=>Array.isArray(t)?t:[t];function Oe(t){return xe[t]&&(t=xe[t]),je(t)?ze(t):t}var _e={get:(t,e)=>{e=Oe(e);let r=Se(e)?t.style.getPropertyValue(e):getComputedStyle(t)[e];if(!r&&r!==0){let i=J.get(e);i&&(r=i.initialValue)}return r},set:(t,e,r)=>{e=Oe(e),Se(e)?t.style.setProperty(e,r):t.style[e]=r}};function Be(t,e=!0){if(!(!t||t.playState==="finished"))try{t.stop?t.stop():(e&&t.commitStyles(),t.cancel())}catch{}}function Ht(t,e){var r;let i=e?.toDefaultUnit||R,n=t[t.length-1];if(H(n)){let o=((r=n.match(/(-?[\d.]+)([a-z%]*)/))===null||r===void 0?void 0:r[2])||"";o&&(i=s=>s+o)}return i}function li(){return window.__MOTION_DEV_TOOLS_RECORD}function We(t,e,r,i={},n){let o=li(),s=i.record!==!1&&o,l,{duration:u=_.duration,delay:f=_.delay,endDelay:m=_.endDelay,repeat:p=_.repeat,easing:d=_.easing,persist:O=!1,direction:E,offset:T,allowWebkitAcceleration:F=!1,autoplay:A=!0}=i,M=Me(t),k=je(e),j=Q.waapi();k&&$t(t,e);let D=Oe(e),z=zt(M.values,D),$=J.get(D);return Be(z.animation,!(X(d)&&z.generator)&&i.record!==!1),()=>{let L=()=>{var w,v;return(v=(w=_e.get(t,D))!==null&&w!==void 0?w:$?.initialValue)!==null&&v!==void 0?v:0},x=Kt($e(r),L),W=Ht(x,$);if(X(d)){let w=d.createAnimation(x,e!=="opacity",L,D,z);d=w.easing,x=w.keyframes||x,u=w.duration||u}if(Se(D)&&(Q.cssRegisterProperty()?Wt(D):j=!1),k&&!Q.linearEasing()&&(N(d)||K(d)&&d.some(N))&&(j=!1),j){$&&(x=x.map(a=>C(a)?$.toDefaultUnit(a):a)),x.length===1&&(!Q.partialKeyframes()||s)&&x.unshift(L());let w={delay:Y.ms(f),duration:Y.ms(u),endDelay:Y.ms(m),easing:K(d)?void 0:lt(d,u),direction:E,iterations:p+1,fill:"both"};l=t.animate({[D]:x,offset:T,easing:K(d)?d.map(a=>lt(a,u)):void 0},w),l.finished||(l.finished=new Promise((a,c)=>{l.onfinish=a,l.oncancel=c}));let v=x[x.length-1];l.finished.then(()=>{O||(_e.set(t,D,v),l.cancel())}).catch(ge),F||(l.playbackRate=1.000001)}else if(n&&k)x=x.map(w=>typeof w=="string"?parseFloat(w):w),x.length===1&&x.unshift(parseFloat(L())),l=new n(w=>{_e.set(t,D,W?W(w):w)},x,Object.assign(Object.assign({},i),{duration:u,easing:d}));else{let w=x[x.length-1];_e.set(t,D,$&&C(w)?$.toDefaultUnit(w):w)}return s&&o(t,e,x,{duration:u,delay:f,easing:d,repeat:p,offset:T},"motion-one"),z.setAnimation(l),l&&!A&&l.pause(),l}}var Ve=(t,e)=>t[e]?Object.assign(Object.assign({},t),t[e]):Object.assign({},t);function ee(t,e){var r;return typeof t=="string"?e?((r=e[t])!==null&&r!==void 0||(e[t]=document.querySelectorAll(t)),t=e[t]):t=document.querySelectorAll(t):t instanceof Element&&(t=[t]),Array.from(t||[])}var ci=t=>t(),le=(t,e,r=_.duration)=>new Proxy({animations:t.map(ci).filter(Boolean),duration:r,options:e},ui),fi=t=>t.animations[0],ui={get:(t,e)=>{let r=fi(t);switch(e){case"duration":return t.duration;case"currentTime":return Y.s(r?.[e]||0);case"playbackRate":case"playState":return r?.[e];case"finished":return t.finished||(t.finished=Promise.all(t.animations.map(di)).catch(ge)),t.finished;case"stop":return()=>{t.animations.forEach(i=>Be(i))};case"forEachNative":return i=>{t.animations.forEach(n=>i(n,t))};default:return typeof r?.[e]>"u"?void 0:()=>t.animations.forEach(i=>i[e]())}},set:(t,e,r)=>{switch(e){case"currentTime":r=Y.ms(r);case"playbackRate":for(let i=0;i<t.animations.length;i++)t.animations[i][e]=r;return!0}return!1}},di=t=>t.finished;function Gt(t=.1,{start:e=0,from:r=0,easing:i}={}){return(n,o)=>{let s=C(r)?r:hi(r,o),l=Math.abs(s-n),u=t*l;if(i){let f=o*t;u=fe(i)(u/f)*f}return e+u}}function hi(t,e){if(t==="first")return 0;{let r=e-1;return t==="last"?r:r/2}}function Ke(t,e,r){return N(t)?t(e,r):t}function Ut(t){return function(r,i,n={}){r=ee(r);let o=r.length;we(!!o,"No valid element provided."),we(!!i,"No keyframes defined.");let s=[];for(let l=0;l<o;l++){let u=r[l];for(let f in i){let m=Ve(n,f);m.delay=Ke(m.delay,l,o);let p=We(u,f,i[f],m,t);s.push(p)}}return le(s,n,n.duration)}}var ct=Ut(G);function He(t,e){var r={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(r[i]=t[i]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,i=Object.getOwnPropertySymbols(t);n<i.length;n++)e.indexOf(i[n])<0&&Object.prototype.propertyIsEnumerable.call(t,i[n])&&(r[i[n]]=t[i[n]]);return r}function ft(t,e,r,i){var n;return C(e)?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):e==="<"?r:(n=i.get(e))!==null&&n!==void 0?n:t}function pi(t,e,r){for(let i=0;i<t.length;i++){let n=t[i];n.at>e&&n.at<r&&(rt(t,n),i--)}}function qt(t,e,r,i,n,o){pi(t,n,o);for(let s=0;s<e.length;s++)t.push({value:e[s],at:se(n,o,i[s]),easing:ve(r,s)})}function Zt(t,e){return t.at===e.at?t.value===null?1:-1:t.at-e.at}function Xt(t,e={}){var r;let i=mi(t,e),n=i.map(o=>We(...o,G)).filter(Boolean);return le(n,e,(r=i[0])===null||r===void 0?void 0:r[3].duration)}function mi(t,e={}){var{defaultOptions:r={}}=e,i=He(e,["defaultOptions"]);let n=[],o=new Map,s={},l=new Map,u=0,f=0,m=0;for(let p=0;p<t.length;p++){let d=t[p];if(H(d)){l.set(d,f);continue}else if(!Array.isArray(d)){l.set(d.name,ft(f,d.at,u,l));continue}let[O,E,T={}]=d;T.at!==void 0&&(f=ft(f,T.at,u,l));let F=0,A=ee(O,s),M=A.length;for(let k=0;k<M;k++){let j=A[k],D=vi(j,o);for(let z in E){let $=gi(z,D),L=$e(E[z]),x=Ve(T,z),{duration:W=r.duration||_.duration,easing:w=r.easing||_.easing}=x;if(X(w)){we(z==="opacity"||L.length>1,"spring must be provided 2 keyframes within timeline()");let y=w.createAnimation(L,z!=="opacity",()=>0,z);w=y.easing,L=y.keyframes||L,W=y.duration||W}let v=Ke(T.delay,k,M)||0,a=f+v,c=a+W,{offset:h=oe(L.length)}=x;h.length===1&&h[0]===0&&(h[1]=1);let g=h.length-L.length;g>0&&ce(h,g),L.length===1&&L.unshift(null),qt($,L,w,h,a,c),F=Math.max(v+W,F),m=Math.max(c,m)}}u=f,f+=F}return o.forEach((p,d)=>{for(let O in p){let E=p[O];E.sort(Zt);let T=[],F=[],A=[];for(let M=0;M<E.length;M++){let{at:k,value:j,easing:D}=E[M];T.push(j),F.push(V(0,m,k)),A.push(D||_.easing)}F[0]!==0&&(F.unshift(0),T.unshift(T[0]),A.unshift("linear")),F[F.length-1]!==1&&(F.push(1),T.push(null)),n.push([d,O,T,Object.assign(Object.assign(Object.assign({},r),{duration:m,easing:A,offset:F}),i)])}}),n}function vi(t,e){return!e.has(t)&&e.set(t,{}),e.get(t)}function gi(t,e){return e[t]||(e[t]=[]),e[t]}var yi={any:0,all:1};function Yt(t,e,{root:r,margin:i,amount:n="any"}={}){if(typeof IntersectionObserver>"u")return()=>{};let o=ee(t),s=new WeakMap,l=f=>{f.forEach(m=>{let p=s.get(m.target);if(m.isIntersecting!==!!p)if(m.isIntersecting){let d=e(m);N(d)?s.set(m.target,d):u.unobserve(m.target)}else p&&(p(m),s.delete(m.target))})},u=new IntersectionObserver(l,{root:r,rootMargin:i,threshold:typeof n=="number"?n:yi[n]});return o.forEach(f=>u.observe(f)),()=>u.disconnect()}var Ge=new WeakMap,te;function bi(t,e){if(e){let{inlineSize:r,blockSize:i}=e[0];return{width:r,height:i}}else return t instanceof SVGElement&&"getBBox"in t?t.getBBox():{width:t.offsetWidth,height:t.offsetHeight}}function wi({target:t,contentRect:e,borderBoxSize:r}){var i;(i=Ge.get(t))===null||i===void 0||i.forEach(n=>{n({target:t,contentSize:e,get size(){return bi(t,r)}})})}function Ei(t){t.forEach(wi)}function xi(){typeof ResizeObserver>"u"||(te=new ResizeObserver(Ei))}function Jt(t,e){te||xi();let r=ee(t);return r.forEach(i=>{let n=Ge.get(i);n||(n=new Set,Ge.set(i,n)),n.add(e),te?.observe(i)}),()=>{r.forEach(i=>{let n=Ge.get(i);n?.delete(e),n?.size||te?.unobserve(i)})}}var Ue=new Set,Te;function Si(){Te=()=>{let t={width:window.innerWidth,height:window.innerHeight},e={target:window,size:t,contentSize:t};Ue.forEach(r=>r(e))},window.addEventListener("resize",Te)}function Qt(t){return Ue.add(t),Te||Si(),()=>{Ue.delete(t),!Ue.size&&Te&&(Te=void 0)}}function er(t,e){return N(t)?Qt(t):Jt(t,e)}var Oi=50,tr=()=>({current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}),ir=()=>({time:0,x:tr(),y:tr()}),_i={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function rr(t,e,r,i){let n=r[e],{length:o,position:s}=_i[e],l=n.current,u=r.time;n.current=t[`scroll${s}`],n.scrollLength=t[`scroll${o}`]-t[`client${o}`],n.offset.length=0,n.offset[0]=0,n.offset[1]=n.scrollLength,n.progress=V(0,n.scrollLength,n.current);let f=i-u;n.velocity=f>Oi?0:it(n.current-l,f)}function nr(t,e,r){rr(t,"x",e,r),rr(t,"y",e,r),e.time=r}function sr(t,e){let r={x:0,y:0},i=t;for(;i&&i!==e;)if(i instanceof HTMLElement)r.x+=i.offsetLeft,r.y+=i.offsetTop,i=i.offsetParent;else if(i instanceof SVGGraphicsElement&&"getBBox"in i){let{top:n,left:o}=i.getBBox();for(r.x+=o,r.y+=n;i&&i.tagName!=="svg";)i=i.parentNode}return r}var qe={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]};var Ze={start:0,center:.5,end:1};function ut(t,e,r=0){let i=0;if(Ze[t]!==void 0&&(t=Ze[t]),H(t)){let n=parseFloat(t);t.endsWith("px")?i=n:t.endsWith("%")?t=n/100:t.endsWith("vw")?i=n/100*document.documentElement.clientWidth:t.endsWith("vh")?i=n/100*document.documentElement.clientHeight:t=n}return C(t)&&(i=e*t),r+i}var Ti=[0,0];function or(t,e,r,i){let n=Array.isArray(t)?t:Ti,o=0,s=0;return C(t)?n=[t,t]:H(t)&&(t=t.trim(),t.includes(" ")?n=t.split(" "):n=[t,Ze[t]?t:"0"]),o=ut(n[0],r,i),s=ut(n[1],e),o-s}var Ai={x:0,y:0};function ar(t,e,r){let{offset:i=qe.All}=r,{target:n=t,axis:o="y"}=r,s=o==="y"?"height":"width",l=n!==t?sr(n,t):Ai,u=n===t?{width:t.scrollWidth,height:t.scrollHeight}:{width:n.clientWidth,height:n.clientHeight},f={width:t.clientWidth,height:t.clientHeight};e[o].offset.length=0;let m=!e[o].interpolate,p=i.length;for(let d=0;d<p;d++){let O=or(i[d],f[s],u[s],l[o]);!m&&O!==e[o].interpolatorOffsets[d]&&(m=!0),e[o].offset[d]=O}m&&(e[o].interpolate=ye(oe(p),e[o].offset),e[o].interpolatorOffsets=[...e[o].offset]),e[o].progress=e[o].interpolate(e[o].current)}function Fi(t,e=t,r){if(r.x.targetOffset=0,r.y.targetOffset=0,e!==t){let i=e;for(;i&&i!=t;)r.x.targetOffset+=i.offsetLeft,r.y.targetOffset+=i.offsetTop,i=i.offsetParent}r.x.targetLength=e===t?e.scrollWidth:e.clientWidth,r.y.targetLength=e===t?e.scrollHeight:e.clientHeight,r.x.containerLength=t.clientWidth,r.y.containerLength=t.clientHeight}function lr(t,e,r,i={}){let n=i.axis||"y";return{measure:()=>Fi(t,i.target,r),update:o=>{nr(t,r,o),(i.offset||i.target)&&ar(t,r,i)},notify:N(e)?()=>e(r):Ii(e,r[n])}}function Ii(t,e){return t.pause(),t.forEachNative((r,{easing:i})=>{var n,o;if(r.updateDuration)i||(r.easing=R),r.updateDuration(1);else{let s={duration:1e3};i||(s.easing="linear"),(o=(n=r.effect)===null||n===void 0?void 0:n.updateTiming)===null||o===void 0||o.call(n,s)}}),()=>{t.currentTime=e.progress}}var Ae=new WeakMap,cr=new WeakMap,dt=new WeakMap,fr=t=>t===document.documentElement?window:t;function ur(t,e={}){var{container:r=document.documentElement}=e,i=He(e,["container"]);let n=dt.get(r);n||(n=new Set,dt.set(r,n));let o=ir(),s=lr(r,t,o,i);if(n.add(s),!Ae.has(r)){let f=()=>{let p=performance.now();for(let d of n)d.measure();for(let d of n)d.update(p);for(let d of n)d.notify()};Ae.set(r,f);let m=fr(r);window.addEventListener("resize",f,{passive:!0}),r!==document.documentElement&&cr.set(r,er(r,f)),m.addEventListener("scroll",f,{passive:!0})}let l=Ae.get(r),u=requestAnimationFrame(l);return()=>{var f;typeof t!="function"&&t.stop(),cancelAnimationFrame(u);let m=dt.get(r);if(!m||(m.delete(s),m.size))return;let p=Ae.get(r);Ae.delete(r),p&&(fr(r).removeEventListener("scroll",p),(f=cr.get(r))===null||f===void 0||f(),window.removeEventListener("resize",p))}}function Di(t,e={}){return le([()=>{let r=new G(t,[0,1],e);return r.finished.catch(()=>{}),r}],e,e.duration)}function dr(t,e,r){return(N(t)?Di:ct)(t,e,r)}function U(t){this.listenerMap=[{},{}],t&&this.root(t),this.handle=U.prototype.handle.bind(this),this._removedListeners=[]}U.prototype.root=function(t){let e=this.listenerMap,r;if(this.rootElement){for(r in e[1])e[1].hasOwnProperty(r)&&this.rootElement.removeEventListener(r,this.handle,!0);for(r in e[0])e[0].hasOwnProperty(r)&&this.rootElement.removeEventListener(r,this.handle,!1)}if(!t||!t.addEventListener)return this.rootElement&&delete this.rootElement,this;this.rootElement=t;for(r in e[1])e[1].hasOwnProperty(r)&&this.rootElement.addEventListener(r,this.handle,!0);for(r in e[0])e[0].hasOwnProperty(r)&&this.rootElement.addEventListener(r,this.handle,!1);return this};U.prototype.captureForType=function(t){return["blur","error","focus","load","resize","scroll"].indexOf(t)!==-1};U.prototype.on=function(t,e,r,i){let n,o,s,l;if(!t)throw new TypeError("Invalid event type: "+t);if(typeof e=="function"&&(i=r,r=e,e=null),i===void 0&&(i=this.captureForType(t)),typeof r!="function")throw new TypeError("Handler must be a type of Function");return n=this.rootElement,o=this.listenerMap[i?1:0],o[t]||(n&&n.addEventListener(t,this.handle,i),o[t]=[]),e?/^[a-z]+$/i.test(e)?(l=e,s=Pi):/^#[a-z0-9\-_]+$/i.test(e)?(l=e.slice(1),s=Li):(l=e,s=Element.prototype.matches):(l=null,s=Ni.bind(this)),o[t].push({selector:e,handler:r,matcher:s,matcherParam:l}),this};U.prototype.off=function(t,e,r,i){let n,o,s,l,u;if(typeof e=="function"&&(i=r,r=e,e=null),i===void 0)return this.off(t,e,r,!0),this.off(t,e,r,!1),this;if(s=this.listenerMap[i?1:0],!t){for(u in s)s.hasOwnProperty(u)&&this.off(u,e,r);return this}if(l=s[t],!l||!l.length)return this;for(n=l.length-1;n>=0;n--)o=l[n],(!e||e===o.selector)&&(!r||r===o.handler)&&(this._removedListeners.push(o),l.splice(n,1));return l.length||(delete s[t],this.rootElement&&this.rootElement.removeEventListener(t,this.handle,i)),this};U.prototype.handle=function(t){let e,r,i=t.type,n,o,s,l,u=[],f,m="ftLabsDelegateIgnore";if(t[m]===!0)return;switch(f=t.target,f.nodeType===3&&(f=f.parentNode),f.correspondingUseElement&&(f=f.correspondingUseElement),n=this.rootElement,o=t.eventPhase||(t.target!==t.currentTarget?3:2),o){case 1:u=this.listenerMap[1][i];break;case 2:this.listenerMap[0]&&this.listenerMap[0][i]&&(u=u.concat(this.listenerMap[0][i])),this.listenerMap[1]&&this.listenerMap[1][i]&&(u=u.concat(this.listenerMap[1][i]));break;case 3:u=this.listenerMap[0][i];break}let p=[];for(r=u.length;f&&r;){for(e=0;e<r&&(s=u[e],!!s);e++)f.tagName&&["button","input","select","textarea"].indexOf(f.tagName.toLowerCase())>-1&&f.hasAttribute("disabled")?p=[]:s.matcher.call(f,s.matcherParam,f)&&p.push([t,f,s]);if(f===n||(r=u.length,f=f.parentElement||f.parentNode,f instanceof HTMLDocument))break}let d;for(e=0;e<p.length;e++)if(!(this._removedListeners.indexOf(p[e][2])>-1)&&(l=this.fire.apply(this,p[e]),l===!1)){p[e][0][m]=!0,p[e][0].preventDefault(),d=!1;break}return d};U.prototype.fire=function(t,e,r){return r.handler.call(e,t,e)};function Pi(t,e){return t.toLowerCase()===e.tagName.toLowerCase()}function Ni(t,e){return this.rootElement===window?e===document||e===document.documentElement||e===window:this.rootElement===e}function Li(t,e){return t===e.id}U.prototype.destroy=function(){this.off(),this.root()};var Ri=U;function Fe(t,e,r){let i=document.createElement(e);return t&&(i.className=t),r&&r.appendChild(i),i}function Ci(t,e,r){let i=`translate3d(${t}px,${e||0}px,0)`;return r!==void 0&&(i+=` scale3d(${r},${r},1)`),i}function ht(t,e,r){t.style.width=typeof e=="number"?`${e}px`:e,t.style.height=typeof r=="number"?`${r}px`:r}var B={IDLE:"idle",LOADING:"loading",LOADED:"loaded",ERROR:"error"};function Mi(t){return"button"in t&&t.button===1||t.ctrlKey||t.metaKey||t.altKey||t.shiftKey}function Ie(t,e,r=document){let i=[];if(t instanceof Element)i=[t];else if(t instanceof NodeList||Array.isArray(t))i=Array.from(t);else{let n=typeof t=="string"?t:e;n&&(i=Array.from(r.querySelectorAll(n)))}return i}function ki(t){return typeof t=="function"&&t.prototype&&t.prototype.goTo}function hr(){return!!(navigator.vendor&&navigator.vendor.match(/apple/i))}var pt=class{constructor(e,r){this.type=e,this.defaultPrevented=!1,r&&Object.assign(this,r)}preventDefault(){this.defaultPrevented=!0}},mt=class{constructor(){this._listeners={},this._filters={},this.pswp=void 0,this.options=void 0}addFilter(e,r,i=100){var n,o,s;this._filters[e]||(this._filters[e]=[]),(n=this._filters[e])===null||n===void 0||n.push({fn:r,priority:i}),(o=this._filters[e])===null||o===void 0||o.sort((l,u)=>l.priority-u.priority),(s=this.pswp)===null||s===void 0||s.addFilter(e,r,i)}removeFilter(e,r){this._filters[e]&&(this._filters[e]=this._filters[e].filter(i=>i.fn!==r)),this.pswp&&this.pswp.removeFilter(e,r)}applyFilters(e,...r){var i;return(i=this._filters[e])===null||i===void 0||i.forEach(n=>{r[0]=n.fn.apply(this,r)}),r[0]}on(e,r){var i,n;this._listeners[e]||(this._listeners[e]=[]),(i=this._listeners[e])===null||i===void 0||i.push(r),(n=this.pswp)===null||n===void 0||n.on(e,r)}off(e,r){var i;this._listeners[e]&&(this._listeners[e]=this._listeners[e].filter(n=>r!==n)),(i=this.pswp)===null||i===void 0||i.off(e,r)}dispatch(e,r){var i;if(this.pswp)return this.pswp.dispatch(e,r);let n=new pt(e,r);return(i=this._listeners[e])===null||i===void 0||i.forEach(o=>{o.call(this,n)}),n}},vt=class{constructor(e,r){if(this.element=Fe("pswp__img pswp__img--placeholder",e?"img":"div",r),e){let i=this.element;i.decoding="async",i.alt="",i.src=e,i.setAttribute("role","presentation")}this.element.setAttribute("aria-hidden","true")}setDisplayedSize(e,r){this.element&&(this.element.tagName==="IMG"?(ht(this.element,250,"auto"),this.element.style.transformOrigin="0 0",this.element.style.transform=Ci(0,0,e/250)):ht(this.element,e,r))}destroy(){var e;(e=this.element)!==null&&e!==void 0&&e.parentNode&&this.element.remove(),this.element=null}},gt=class{constructor(e,r,i){this.instance=r,this.data=e,this.index=i,this.element=void 0,this.placeholder=void 0,this.slide=void 0,this.displayedImageWidth=0,this.displayedImageHeight=0,this.width=Number(this.data.w)||Number(this.data.width)||0,this.height=Number(this.data.h)||Number(this.data.height)||0,this.isAttached=!1,this.hasSlide=!1,this.isDecoding=!1,this.state=B.IDLE,this.data.type?this.type=this.data.type:this.data.src?this.type="image":this.type="html",this.instance.dispatch("contentInit",{content:this})}removePlaceholder(){this.placeholder&&!this.keepPlaceholder()&&setTimeout(()=>{this.placeholder&&(this.placeholder.destroy(),this.placeholder=void 0)},1e3)}load(e,r){if(this.slide&&this.usePlaceholder())if(this.placeholder){let i=this.placeholder.element;i&&!i.parentElement&&this.slide.container.prepend(i)}else{let i=this.instance.applyFilters("placeholderSrc",this.data.msrc&&this.slide.isFirstSlide?this.data.msrc:!1,this);this.placeholder=new vt(i,this.slide.container)}this.element&&!r||this.instance.dispatch("contentLoad",{content:this,isLazy:e}).defaultPrevented||(this.isImageContent()?(this.element=Fe("pswp__img","img"),this.displayedImageWidth&&this.loadImage(e)):(this.element=Fe("pswp__content","div"),this.element.innerHTML=this.data.html||""),r&&this.slide&&this.slide.updateContentSize(!0))}loadImage(e){var r,i;if(!this.isImageContent()||!this.element||this.instance.dispatch("contentLoadImage",{content:this,isLazy:e}).defaultPrevented)return;let n=this.element;this.updateSrcsetSizes(),this.data.srcset&&(n.srcset=this.data.srcset),n.src=(r=this.data.src)!==null&&r!==void 0?r:"",n.alt=(i=this.data.alt)!==null&&i!==void 0?i:"",this.state=B.LOADING,n.complete?this.onLoaded():(n.onload=()=>{this.onLoaded()},n.onerror=()=>{this.onError()})}setSlide(e){this.slide=e,this.hasSlide=!0,this.instance=e.pswp}onLoaded(){this.state=B.LOADED,this.slide&&this.element&&(this.instance.dispatch("loadComplete",{slide:this.slide,content:this}),this.slide.isActive&&this.slide.heavyAppended&&!this.element.parentNode&&(this.append(),this.slide.updateContentSize(!0)),(this.state===B.LOADED||this.state===B.ERROR)&&this.removePlaceholder())}onError(){this.state=B.ERROR,this.slide&&(this.displayError(),this.instance.dispatch("loadComplete",{slide:this.slide,isError:!0,content:this}),this.instance.dispatch("loadError",{slide:this.slide,content:this}))}isLoading(){return this.instance.applyFilters("isContentLoading",this.state===B.LOADING,this)}isError(){return this.state===B.ERROR}isImageContent(){return this.type==="image"}setDisplayedSize(e,r){if(this.element&&(this.placeholder&&this.placeholder.setDisplayedSize(e,r),!this.instance.dispatch("contentResize",{content:this,width:e,height:r}).defaultPrevented&&(ht(this.element,e,r),this.isImageContent()&&!this.isError()))){let i=!this.displayedImageWidth&&e;this.displayedImageWidth=e,this.displayedImageHeight=r,i?this.loadImage(!1):this.updateSrcsetSizes(),this.slide&&this.instance.dispatch("imageSizeChange",{slide:this.slide,width:e,height:r,content:this})}}isZoomable(){return this.instance.applyFilters("isContentZoomable",this.isImageContent()&&this.state!==B.ERROR,this)}updateSrcsetSizes(){if(!this.isImageContent()||!this.element||!this.data.srcset)return;let e=this.element,r=this.instance.applyFilters("srcsetSizesWidth",this.displayedImageWidth,this);(!e.dataset.largestUsedSize||r>parseInt(e.dataset.largestUsedSize,10))&&(e.sizes=r+"px",e.dataset.largestUsedSize=String(r))}usePlaceholder(){return this.instance.applyFilters("useContentPlaceholder",this.isImageContent(),this)}lazyLoad(){this.instance.dispatch("contentLazyLoad",{content:this}).defaultPrevented||this.load(!0)}keepPlaceholder(){return this.instance.applyFilters("isKeepingPlaceholder",this.isLoading(),this)}destroy(){this.hasSlide=!1,this.slide=void 0,!this.instance.dispatch("contentDestroy",{content:this}).defaultPrevented&&(this.remove(),this.placeholder&&(this.placeholder.destroy(),this.placeholder=void 0),this.isImageContent()&&this.element&&(this.element.onload=null,this.element.onerror=null,this.element=void 0))}displayError(){if(this.slide){var e,r;let i=Fe("pswp__error-msg","div");i.innerText=(e=(r=this.instance.options)===null||r===void 0?void 0:r.errorMsg)!==null&&e!==void 0?e:"",i=this.instance.applyFilters("contentErrorElement",i,this),this.element=Fe("pswp__content pswp__error-msg-container","div"),this.element.appendChild(i),this.slide.container.innerText="",this.slide.container.appendChild(this.element),this.slide.updateContentSize(!0),this.removePlaceholder()}}append(){if(this.isAttached||!this.element)return;if(this.isAttached=!0,this.state===B.ERROR){this.displayError();return}if(this.instance.dispatch("contentAppend",{content:this}).defaultPrevented)return;let e="decode"in this.element;this.isImageContent()?e&&this.slide&&(!this.slide.isActive||hr())?(this.isDecoding=!0,this.element.decode().catch(()=>{}).finally(()=>{this.isDecoding=!1,this.appendImage()})):this.appendImage():this.slide&&!this.element.parentNode&&this.slide.container.appendChild(this.element)}activate(){this.instance.dispatch("contentActivate",{content:this}).defaultPrevented||!this.slide||(this.isImageContent()&&this.isDecoding&&!hr()?this.appendImage():this.isError()&&this.load(!1,!0),this.slide.holderElement&&this.slide.holderElement.setAttribute("aria-hidden","false"))}deactivate(){this.instance.dispatch("contentDeactivate",{content:this}),this.slide&&this.slide.holderElement&&this.slide.holderElement.setAttribute("aria-hidden","true")}remove(){this.isAttached=!1,!this.instance.dispatch("contentRemove",{content:this}).defaultPrevented&&(this.element&&this.element.parentNode&&this.element.remove(),this.placeholder&&this.placeholder.element&&this.placeholder.element.remove())}appendImage(){this.isAttached&&(this.instance.dispatch("contentAppendImage",{content:this}).defaultPrevented||(this.slide&&this.element&&!this.element.parentNode&&this.slide.container.appendChild(this.element),(this.state===B.LOADED||this.state===B.ERROR)&&this.removePlaceholder()))}};function zi(t,e){if(t.getViewportSizeFn){let r=t.getViewportSizeFn(t,e);if(r)return r}return{x:document.documentElement.clientWidth,y:window.innerHeight}}function Xe(t,e,r,i,n){let o=0;if(e.paddingFn)o=e.paddingFn(r,i,n)[t];else if(e.padding)o=e.padding[t];else{let s="padding"+t[0].toUpperCase()+t.slice(1);e[s]&&(o=e[s])}return Number(o)||0}function ji(t,e,r,i){return{x:e.x-Xe("left",t,e,r,i)-Xe("right",t,e,r,i),y:e.y-Xe("top",t,e,r,i)-Xe("bottom",t,e,r,i)}}var pr=4e3,yt=class{constructor(e,r,i,n){this.pswp=n,this.options=e,this.itemData=r,this.index=i,this.panAreaSize=null,this.elementSize=null,this.fit=1,this.fill=1,this.vFill=1,this.initial=1,this.secondary=1,this.max=1,this.min=1}update(e,r,i){let n={x:e,y:r};this.elementSize=n,this.panAreaSize=i;let o=i.x/n.x,s=i.y/n.y;this.fit=Math.min(1,o<s?o:s),this.fill=Math.min(1,o>s?o:s),this.vFill=Math.min(1,s),this.initial=this._getInitial(),this.secondary=this._getSecondary(),this.max=Math.max(this.initial,this.secondary,this._getMax()),this.min=Math.min(this.fit,this.initial,this.secondary),this.pswp&&this.pswp.dispatch("zoomLevelsUpdate",{zoomLevels:this,slideData:this.itemData})}_parseZoomLevelOption(e){let r=e+"ZoomLevel",i=this.options[r];if(i)return typeof i=="function"?i(this):i==="fill"?this.fill:i==="fit"?this.fit:Number(i)}_getSecondary(){let e=this._parseZoomLevelOption("secondary");return e||(e=Math.min(1,this.fit*3),this.elementSize&&e*this.elementSize.x>pr&&(e=pr/this.elementSize.x),e)}_getInitial(){return this._parseZoomLevelOption("initial")||this.fit}_getMax(){return this._parseZoomLevelOption("max")||Math.max(1,this.fit*4)}};function mr(t,e,r){let i=e.createContentFromData(t,r),n,{options:o}=e;if(o){n=new yt(o,t,-1);let s;e.pswp?s=e.pswp.viewportSize:s=zi(o,e);let l=ji(o,s,t,r);n.update(i.width,i.height,l)}return i.lazyLoad(),n&&i.setDisplayedSize(Math.ceil(i.width*n.initial),Math.ceil(i.height*n.initial)),i}function $i(t,e){let r=e.getItemData(t);if(!e.dispatch("lazyLoadSlide",{index:t,itemData:r}).defaultPrevented)return mr(r,e,t)}var bt=class extends mt{getNumItems(){var e;let r=0,i=(e=this.options)===null||e===void 0?void 0:e.dataSource;i&&"length"in i?r=i.length:i&&"gallery"in i&&(i.items||(i.items=this._getGalleryDOMElements(i.gallery)),i.items&&(r=i.items.length));let n=this.dispatch("numItems",{dataSource:i,numItems:r});return this.applyFilters("numItems",n.numItems,i)}createContentFromData(e,r){return new gt(e,this,r)}getItemData(e){var r;let i=(r=this.options)===null||r===void 0?void 0:r.dataSource,n={};Array.isArray(i)?n=i[e]:i&&"gallery"in i&&(i.items||(i.items=this._getGalleryDOMElements(i.gallery)),n=i.items[e]);let o=n;o instanceof Element&&(o=this._domElementToItemData(o));let s=this.dispatch("itemData",{itemData:o||{},index:e});return this.applyFilters("itemData",s.itemData,e)}_getGalleryDOMElements(e){var r,i;return(r=this.options)!==null&&r!==void 0&&r.children||(i=this.options)!==null&&i!==void 0&&i.childSelector?Ie(this.options.children,this.options.childSelector,e)||[]:[e]}_domElementToItemData(e){let r={element:e},i=e.tagName==="A"?e:e.querySelector("a");if(i){r.src=i.dataset.pswpSrc||i.href,i.dataset.pswpSrcset&&(r.srcset=i.dataset.pswpSrcset),r.width=i.dataset.pswpWidth?parseInt(i.dataset.pswpWidth,10):0,r.height=i.dataset.pswpHeight?parseInt(i.dataset.pswpHeight,10):0,r.w=r.width,r.h=r.height,i.dataset.pswpType&&(r.type=i.dataset.pswpType);let o=e.querySelector("img");if(o){var n;r.msrc=o.currentSrc||o.src,r.alt=(n=o.getAttribute("alt"))!==null&&n!==void 0?n:""}(i.dataset.pswpCropped||i.dataset.cropped)&&(r.thumbCropped=!0)}return this.applyFilters("domItemData",r,e,i)}lazyLoadData(e,r){return mr(e,this,r)}},wt=class extends bt{constructor(e){super(),this.options=e||{},this._uid=0,this.shouldOpen=!1,this._preloadedContent=void 0,this.onThumbnailsClick=this.onThumbnailsClick.bind(this)}init(){Ie(this.options.gallery,this.options.gallerySelector).forEach(e=>{e.addEventListener("click",this.onThumbnailsClick,!1)})}onThumbnailsClick(e){if(Mi(e)||window.pswp)return;let r={x:e.clientX,y:e.clientY};!r.x&&!r.y&&(r=null);let i=this.getClickedIndex(e);i=this.applyFilters("clickedIndex",i,e,this);let n={gallery:e.currentTarget};i>=0&&(e.preventDefault(),this.loadAndOpen(i,n,r))}getClickedIndex(e){if(this.options.getClickedIndexFn)return this.options.getClickedIndexFn.call(this,e);let r=e.target,n=Ie(this.options.children,this.options.childSelector,e.currentTarget).findIndex(o=>o===r||o.contains(r));return n!==-1?n:this.options.children||this.options.childSelector?-1:0}loadAndOpen(e,r,i){if(window.pswp||!this.options)return!1;if(!r&&this.options.gallery&&this.options.children){let n=Ie(this.options.gallery);n[0]&&(r={gallery:n[0]})}return this.options.index=e,this.options.initialPointerPos=i,this.shouldOpen=!0,this.preload(e,r),!0}preload(e,r){let{options:i}=this;r&&(i.dataSource=r);let n=[],o=typeof i.pswpModule;if(ki(i.pswpModule))n.push(Promise.resolve(i.pswpModule));else{if(o==="string")throw new Error("pswpModule as string is no longer supported");if(o==="function")n.push(i.pswpModule());else throw new Error("pswpModule is not valid")}typeof i.openPromise=="function"&&n.push(i.openPromise()),i.preloadFirstSlide!==!1&&e>=0&&(this._preloadedContent=$i(e,this));let s=++this._uid;Promise.all(n).then(l=>{if(this.shouldOpen){let u=l[0];this._openPhotoswipe(u,s)}})}_openPhotoswipe(e,r){if(r!==this._uid&&this.shouldOpen||(this.shouldOpen=!1,window.pswp))return;let i=typeof e=="object"?new e.default(this.options):new e(this.options);this.pswp=i,window.pswp=i,Object.keys(this._listeners).forEach(n=>{var o;(o=this._listeners[n])===null||o===void 0||o.forEach(s=>{i.on(n,s)})}),Object.keys(this._filters).forEach(n=>{var o;(o=this._filters[n])===null||o===void 0||o.forEach(s=>{i.addFilter(n,s.fn,s.priority)})}),this._preloadedContent&&(i.contentLoader.addToCache(this._preloadedContent),this._preloadedContent=void 0),i.on("destroy",()=>{this.pswp=void 0,delete window.pswp}),i.init()}destroy(){var e;(e=this.pswp)===null||e===void 0||e.destroy(),this.shouldOpen=!1,this._listeners={},Ie(this.options.gallery,this.options.gallerySelector).forEach(r=>{r.removeEventListener("click",this.onThumbnailsClick,!1)})}};(function(){let t=!1;if(document.createElement("i").addEventListener("click",()=>{},{get signal(){t=!0}}),t||!window.AbortController)return;let e=EventTarget.prototype.addEventListener;EventTarget.prototype.addEventListener=function(r,i,n){if(n&&n.signal){if(n.signal.aborted)return;n.signal.addEventListener("abort",()=>this.removeEventListener(r,i,{...n}))}return e.call(this,r,i,n)}})();export{Ri as Delegate,Ct as FocusTrap,wt as PhotoSwipeLightbox,qe as ScrollOffset,dr as animate,Yt as inView,ur as scroll,Gt as stagger,Xt as timeline};
/*! Bundled license information:

tabbable/dist/index.esm.js:
  (*!
  * tabbable 6.2.0
  * @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
  *)

focus-trap/dist/focus-trap.esm.js:
  (*!
  * focus-trap 7.6.4
  * @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
  *)

photoswipe/dist/photoswipe-lightbox.esm.js:
  (*!
    * PhotoSwipe Lightbox 5.4.4 - https://photoswipe.com
    * (c) 2024 Dmytro Semenov
    *)
*/
