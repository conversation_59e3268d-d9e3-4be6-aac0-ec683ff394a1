[{"name": "theme_info", "theme_name": "Prestige", "theme_author": "Maestrooo", "theme_version": "10.7.0", "theme_documentation_url": "https://support.maestrooo.com/", "theme_support_url": "https://support.maestrooo.com/article/203-contact-us"}, {"name": "t:theme_settings.appearance.name", "settings": [{"type": "paragraph", "content": "t:theme_settings.appearance.info_paragraph"}, {"type": "range", "id": "icon_stroke_width", "min": 1, "max": 2, "step": 0.1, "unit": "px", "label": "t:theme_settings.appearance.icon_thickness", "default": 1.5}, {"type": "header", "content": "t:theme_settings.appearance.section_spacing_category"}, {"type": "select", "id": "section_vertical_spacing", "label": "t:theme_settings.appearance.vertical_space_between_sections", "options": [{"value": "xs", "label": "t:global.sizes.x_small"}, {"value": "sm", "label": "t:global.sizes.small"}, {"value": "md", "label": "t:global.sizes.medium"}, {"value": "lg", "label": "t:global.sizes.large"}, {"value": "xl", "label": "t:global.sizes.x_large"}], "default": "md"}, {"type": "header", "content": "t:theme_settings.appearance.rounding_category"}, {"type": "range", "id": "button_border_radius", "min": 0, "max": 60, "step": 2, "unit": "px", "label": "t:theme_settings.appearance.button_border_radius", "default": 0}, {"type": "range", "id": "input_border_radius", "min": 0, "max": 60, "step": 2, "unit": "px", "label": "t:theme_settings.appearance.input_border_radius", "default": 0}]}, {"name": "t:theme_settings.colors.name", "settings": [{"type": "color_scheme_group", "id": "color_schemes", "definition": [{"type": "header", "content": "t:theme_settings.colors.general_category"}, {"type": "color", "id": "background", "label": "t:global.colors.background", "default": "#efefef"}, {"type": "color_background", "id": "background_gradient", "label": "t:global.colors.background_gradient"}, {"type": "color", "id": "text_color", "label": "t:theme_settings.colors.body_text_color", "default": "#1c1c1c"}, {"type": "header", "content": "t:theme_settings.colors.button_category"}, {"type": "color", "id": "primary_button_background", "label": "t:global.colors.background", "default": "#1c1c1c"}, {"type": "color", "id": "primary_button_text_color", "label": "t:global.colors.text", "default": "#ffffff"}, {"type": "header", "content": "t:theme_settings.colors.button_icon_category", "info": "t:theme_settings.colors.button_icon_category_info"}, {"type": "color", "id": "circle_button_background", "label": "t:global.colors.background", "default": "#ffffff"}, {"type": "color", "id": "circle_button_icon", "label": "t:global.colors.icon", "default": "#1c1c1c"}], "role": {"background": {"solid": "background", "gradient": "background_gradient"}, "text": "text_color", "links": "text_color", "icons": "text_color", "primary_button": "primary_button_background", "primary_button_border": "primary_button_background", "on_primary_button": "primary_button_text_color", "secondary_button": "primary_button_background", "secondary_button_border": "primary_button_background", "on_secondary_button": "primary_button_text_color"}}, {"type": "color_scheme", "id": "default_color_scheme", "label": "t:theme_settings.colors.default_color_scheme", "info": "t:theme_settings.colors.default_color_scheme_info", "default": "scheme-1"}, {"type": "header", "content": "t:theme_settings.colors.modal_category"}, {"type": "color_scheme", "id": "modal_color_scheme", "label": "t:global.colors.scheme", "default": "scheme-2"}, {"type": "header", "content": "t:theme_settings.colors.product_category"}, {"type": "color", "id": "product_rating_color", "label": "t:theme_settings.colors.product_rating_color", "default": "#1c1c1c"}, {"type": "color", "id": "product_on_sale_accent", "label": "t:theme_settings.colors.on_sale_accent", "default": "#e32c2b"}, {"type": "color", "id": "product_sold_out_badge_background", "label": "t:theme_settings.colors.sold_out_badge_background", "default": "#efefef"}, {"type": "color", "id": "product_custom_badge_background", "label": "t:theme_settings.colors.custom_badge", "info": "t:theme_settings.colors.custom_badge_info", "default": "#1c1c1c"}, {"type": "header", "content": "t:theme_settings.colors.notification_category"}, {"type": "color", "id": "success_color", "label": "t:theme_settings.colors.success_color", "default": "#307a07"}, {"type": "color", "id": "warning_color", "label": "t:theme_settings.colors.warning_color", "default": "#ed8a00"}, {"type": "color", "id": "error_color", "label": "t:theme_settings.colors.error_color", "default": "#cb2b2b"}]}, {"name": "t:theme_settings.typography.name", "settings": [{"type": "header", "content": "t:theme_settings.typography.headings_category"}, {"type": "font_picker", "id": "heading_font", "label": "t:theme_settings.typography.heading_font", "info": "t:theme_settings.typography.font_info", "default": "helvetica_n4"}, {"type": "select", "id": "heading_text_transform", "label": "t:theme_settings.typography.heading_casing", "options": [{"value": "normal", "label": "t:theme_settings.typography.heading_casing_options.normal"}, {"value": "lowercase", "label": "t:theme_settings.typography.heading_casing_options.lowercase"}, {"value": "uppercase", "label": "t:theme_settings.typography.heading_casing_options.uppercase"}], "default": "uppercase"}, {"type": "range", "id": "heading_font_size_factor", "min": 0.8, "max": 1.5, "step": 0.1, "label": "t:theme_settings.typography.heading_size_factor", "info": "t:theme_settings.typography.heading_size_factor_info", "default": 1}, {"type": "range", "id": "heading_letter_spacing", "label": "t:theme_settings.typography.heading_letter_spacing", "min": -5, "max": 20, "step": 1, "unit": "%", "default": 0.0}, {"type": "header", "content": "t:theme_settings.typography.body_text_category"}, {"type": "font_picker", "id": "text_font", "label": "t:theme_settings.typography.body_font", "info": "t:theme_settings.typography.font_info", "default": "helvetica_n4"}, {"type": "range", "id": "text_font_size_mobile", "label": "t:theme_settings.typography.body_base_size_mobile", "min": 11, "max": 20, "unit": "px", "default": 14}, {"type": "range", "id": "text_font_size_desktop", "label": "t:theme_settings.typography.body_base_size_desktop", "min": 11, "max": 20, "unit": "px", "default": 16}, {"type": "range", "id": "text_font_letter_spacing", "label": "t:theme_settings.typography.body_letter_spacing", "min": -5, "max": 20, "step": 1, "default": 0.0}, {"type": "header", "content": "t:theme_settings.typography.button_category"}, {"type": "select", "id": "button_text_font", "label": "t:theme_settings.typography.button_text_font", "options": [{"value": "heading", "label": "t:theme_settings.typography.button_text_font_options.heading"}, {"value": "body", "label": "t:theme_settings.typography.button_text_font_options.body"}], "default": "body"}, {"type": "select", "id": "button_text_transform", "label": "t:theme_settings.typography.button_casing", "options": [{"value": "normal", "label": "t:theme_settings.typography.button_casing_options.normal"}, {"value": "lowercase", "label": "t:theme_settings.typography.button_casing_options.lowercase"}, {"value": "uppercase", "label": "t:theme_settings.typography.button_casing_options.uppercase"}], "default": "uppercase"}, {"type": "range", "id": "button_letter_spacing", "label": "t:theme_settings.typography.button_letter_spacing", "min": -5, "max": 20, "step": 1, "unit": "%", "default": 0.0}]}, {"name": "t:theme_settings.currency_format.name", "settings": [{"type": "header", "content": "t:theme_settings.currency_format.currency_codes"}, {"type": "paragraph", "content": "t:theme_settings.currency_format.currency_codes_paragraph"}, {"type": "checkbox", "id": "currency_code_enabled", "label": "t:theme_settings.currency_format.show_currency_codes", "default": false}]}, {"name": "t:theme_settings.animation.name", "settings": [{"type": "paragraph", "content": "t:theme_settings.animation.minimize_motion_paragraph"}, {"type": "checkbox", "id": "show_button_transition", "label": "t:theme_settings.animation.show_button_transition", "default": true}, {"type": "checkbox", "id": "show_image_zoom_on_hover", "label": "t:theme_settings.animation.show_image_zoom_on_hover", "info": "t:theme_settings.animation.show_image_zoom_on_hover_info", "default": true}, {"type": "checkbox", "id": "stagger_products_apparition", "label": "t:theme_settings.animation.stagger_products_apparition", "default": true}, {"type": "checkbox", "id": "stagger_blog_posts_apparition", "label": "t:theme_settings.animation.stagger_blog_posts_apparition", "default": true}, {"type": "checkbox", "id": "stagger_menu_apparition", "label": "t:theme_settings.animation.stagger_menu_apparition", "default": true}]}, {"name": "t:theme_settings.color_swatch.name", "settings": [{"type": "checkbox", "id": "round_color_swatches", "label": "t:theme_settings.color_swatch.round_color_swatches", "default": false}, {"type": "textarea", "id": "color_swatch_config", "label": "t:theme_settings.color_swatch.configuration", "placeholder": "Red:#ff0000\nGreen:#00ff00\nBlue:#0000ff", "info": "t:theme_settings.color_swatch.configuration_info"}]}, {"name": "t:theme_settings.product_card.name", "settings": [{"type": "header", "content": "t:theme_settings.product_card.product_image_category"}, {"type": "select", "id": "product_image_aspect_ratio", "label": "t:theme_settings.product_card.product_image_size", "options": [{"value": "natural", "label": "t:theme_settings.product_card.product_image_size_options.natural"}, {"value": "short", "label": "t:theme_settings.product_card.product_image_size_options.short"}, {"value": "square", "label": "t:theme_settings.product_card.product_image_size_options.square"}, {"value": "portrait", "label": "t:theme_settings.product_card.product_image_size_options.portrait"}, {"value": "tall", "label": "t:theme_settings.product_card.product_image_size_options.tall"}, {"value": "short_crop", "label": "t:theme_settings.product_card.product_image_size_options.short_crop"}, {"value": "square_crop", "label": "t:theme_settings.product_card.product_image_size_options.square_crop"}, {"value": "portrait_crop", "label": "t:theme_settings.product_card.product_image_size_options.portrait_crop"}, {"value": "tall_crop", "label": "t:theme_settings.product_card.product_image_size_options.tall_crop"}], "default": "natural"}, {"type": "checkbox", "id": "show_secondary_image", "label": "t:theme_settings.product_card.show_secondary_image_on_hover", "default": true}, {"type": "header", "content": "t:theme_settings.product_card.product_info_category"}, {"type": "checkbox", "id": "show_vendor", "label": "t:theme_settings.product_card.show_vendor", "default": false}, {"type": "range", "id": "product_title_max_lines", "min": 0, "max": 4, "label": "t:theme_settings.product_card.product_title_max_lines", "default": 0}, {"type": "select", "id": "product_card_text_font", "label": "t:theme_settings.product_card.text_font", "options": [{"value": "heading", "label": "t:theme_settings.product_card.text_font_options.heading"}, {"value": "body", "label": "t:theme_settings.product_card.text_font_options.body"}], "default": "heading"}, {"type": "select", "id": "product_price_strategy", "label": "t:theme_settings.product_card.when_price_varies", "options": [{"value": "from_price", "label": "t:theme_settings.product_card.when_price_varies_options.from_price"}, {"value": "max_price", "label": "t:theme_settings.product_card.when_price_varies_options.max_price"}], "default": "from_price"}, {"type": "select", "id": "product_color_display", "label": "t:theme_settings.product_card.color_display_mode", "options": [{"value": "hide", "label": "t:theme_settings.product_card.color_display_mode_options.hide"}, {"value": "count", "label": "t:theme_settings.product_card.color_display_mode_options.count"}, {"value": "swatch", "label": "t:theme_settings.product_card.color_display_mode_options.swatch"}], "default": "swatch"}, {"type": "header", "content": "t:theme_settings.product_card.rating_category", "info": "t:theme_settings.product_card.rating_category_info"}, {"type": "checkbox", "id": "show_product_rating", "label": "t:theme_settings.product_card.show_product_rating", "default": true}, {"type": "checkbox", "id": "show_product_rating_if_empty", "label": "t:theme_settings.product_card.show_product_rating_if_empty", "default": false}, {"type": "select", "id": "product_rating_mode", "label": "t:theme_settings.product_card.show_product_rating_as", "options": [{"value": "rating", "label": "t:theme_settings.product_card.show_product_rating_as_options.rating"}, {"value": "count", "label": "t:theme_settings.product_card.show_product_rating_as_options.count"}], "default": "rating"}, {"type": "header", "content": "t:theme_settings.product_card.promotion_category"}, {"type": "checkbox", "id": "show_quick_buy", "label": "t:theme_settings.product_card.show_quick_buy", "default": true}, {"type": "checkbox", "id": "show_sold_out_badge", "label": "t:theme_settings.product_card.show_sold_out_badge", "default": true}, {"type": "checkbox", "id": "show_discount", "label": "t:theme_settings.product_card.show_discount_badge", "default": true}, {"type": "select", "id": "discount_mode", "label": "t:theme_settings.product_card.show_discount_as", "options": [{"value": "percentage", "label": "t:theme_settings.product_card.show_discount_as_options.percentage"}, {"value": "saving", "label": "t:theme_settings.product_card.show_discount_as_options.saving"}], "default": "saving"}]}, {"name": "t:theme_settings.cart.name", "settings": [{"type": "select", "id": "cart_type", "label": "t:theme_settings.cart.cart_type", "options": [{"value": "drawer", "label": "t:theme_settings.cart.cart_type_options.drawer"}, {"value": "message", "label": "t:theme_settings.cart.cart_type_options.message"}, {"value": "page", "label": "t:theme_settings.cart.cart_type_options.page"}], "default": "drawer"}, {"type": "select", "id": "cart_icon", "label": "t:theme_settings.cart.cart_icon", "options": [{"value": "shopping_bag", "label": "t:theme_settings.cart.cart_icon_options.shopping_bag"}, {"value": "shopping_bag_sharp", "label": "t:theme_settings.cart.cart_icon_options.shopping_bag_sharp"}, {"value": "shopping_cart", "label": "t:theme_settings.cart.cart_icon_options.shopping_cart"}, {"value": "tote_bag", "label": "t:theme_settings.cart.cart_icon_options.tote_bag"}], "default": "shopping_bag"}, {"type": "url", "id": "cart_empty_cart_link", "label": "t:theme_settings.cart.empty_cart_link", "default": "/collections/all"}, {"type": "header", "content": "t:theme_settings.cart.free_shipping_bar_category", "info": "t:theme_settings.cart.free_shipping_bar_category_info"}, {"type": "checkbox", "id": "cart_show_free_shipping_bar", "label": "t:theme_settings.cart.show_shipping_bar", "default": false}, {"type": "text", "id": "cart_free_shipping_threshold", "label": "t:theme_settings.cart.free_shipping_bar_minimum_amount", "placeholder": "t:theme_settings.cart.free_shipping_bar_minimum_amount_placeholder", "info": "t:theme_settings.cart.free_shipping_bar_minimum_amount_info", "default": "50"}]}, {"name": "t:theme_settings.social_media.name", "settings": [{"type": "header", "content": "t:theme_settings.social_media.accounts"}, {"type": "url", "id": "social_facebook", "label": "Facebook"}, {"type": "url", "id": "social_twitter", "label": "X (formerly Twitter)"}, {"type": "url", "id": "social_pinterest", "label": "Pinterest"}, {"type": "url", "id": "social_threads", "label": "Threads"}, {"type": "url", "id": "social_instagram", "label": "Instagram"}, {"type": "url", "id": "social_vimeo", "label": "Vimeo"}, {"type": "url", "id": "social_whatsapp", "label": "WhatsApp"}, {"type": "url", "id": "social_tumblr", "label": "Tumblr"}, {"type": "url", "id": "social_youtube", "label": "YouTube"}, {"type": "url", "id": "social_tiktok", "label": "TikTok"}, {"type": "url", "id": "social_linkedin", "label": "LinkedIn"}, {"type": "url", "id": "social_snapchat", "label": "Snapchat"}, {"type": "url", "id": "social_wechat", "label": "WeChat"}, {"type": "url", "id": "social_reddit", "label": "Reddit"}, {"type": "url", "id": "social_line", "label": "LINE"}, {"type": "url", "id": "social_spotify", "label": "Spotify"}, {"type": "url", "id": "social_21buttons", "label": "21 Buttons"}]}, {"name": "t:theme_settings.favicon.name", "settings": [{"type": "image_picker", "id": "favicon", "label": "t:theme_settings.favicon.image", "info": "t:theme_settings.favicon.image_info"}]}]