{%- if section.blocks.size > 0 -%}
  {%- assign color_scheme_hash = section.settings.color_scheme.settings.background_gradient | default: section.settings.color_scheme.settings.background | md5 -%}

  <div class="section-spacing color-scheme color-scheme--{{ section.settings.color_scheme.id }} color-scheme--bg-{{ color_scheme_hash }} {% if section.settings.separate_section_with_border %}bordered-section{% endif %}">
    <div class="container {% if section.settings.text_alignment == 'center' %}container--xl{% endif %}">
      <div class="section-stack">
        {%- render 'section-header', subheading: section.settings.subheading, heading: section.settings.title, content: section.settings.content, text_alignment: section.settings.text_alignment -%}

        <!-- DESKTOP LAYOUT -->

        <x-tabs class="content-tabs {% if section.settings.text_alignment == 'center' %}content-tabs--center{% endif %} hidden sm:block">
          <template shadowrootmode="open">
            <slot role="tablist" part="tab-list" name="title"></slot>
            <slot part="tab-panel-list" name="content"></slot>
          </template>

          {%- for block in section.blocks -%}
            {%- liquid
              assign title = block.settings.page.title | default: block.settings.title

              if block.type == 'tab'
                assign content = block.settings.page.content | default: block.settings.content
              elsif block.type == 'liquid'
                assign content = block.settings.liquid
              endif
            -%}

            {%- if title != blank and content != blank -%}
              <button class="h6" type="button" role="tab" slot="title" {{ block.shopify_attributes }}>{{ title }}</button>

              <div role="tabpanel" slot="content" {% cycle '', 'hidden', 'hidden', 'hidden', 'hidden' %}>
                <div class="prose">
                  {{- content -}}
                </div>
              </div>
            {%- endif -%}
          {%- endfor -%}
        </x-tabs>

        <!-- MOBILE LAYOUT -->

        <div class="accordion-group sm:hidden">
          {%- for block in section.blocks -%}
            {%- liquid
              assign title = block.settings.page.title | default: block.settings.title

              if block.type == 'tab'
                assign content = block.settings.page.content | default: block.settings.content
              elsif block.type == 'liquid'
                assign content = block.settings.liquid
              endif
            -%}

            {%- if title != blank and content != blank -%}
              {%- render 'accordion', title: title, content: content, open: block.settings.open_on_mobile, prose_content: true -%}
            {%- endif -%}
          {%- endfor -%}
        </div>
      </div>
    </div>
  </div>
{%- endif -%}

{% schema %}
{
  "name": "t:sections.tabs.name",
  "class": "shopify-section--tabs",
  "max_blocks": 5,
  "disabled_on": {
    "groups": ["header", "custom.overlay"]
  },
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:global.colors.scheme",
      "default": "scheme-1"
    },
    {
      "type": "checkbox",
      "id": "separate_section_with_border",
      "label": "t:global.section.separate_section_with_border",
      "default": true
    },
    {
      "type": "inline_richtext",
      "id": "subheading",
      "label": "t:global.text.subheading",
      "default": "Subheading"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "label": "t:global.text.heading",
      "default": "Heading"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "t:global.text.alignment",
      "options": [
        {
          "value": "start",
          "label": "t:global.position.left"
        },
        {
          "value": "center",
          "label": "t:global.position.center"
        }
      ],
      "default": "center"
    }
  ],
  "blocks": [
    {
      "type": "tab",
      "name": "t:sections.tabs.blocks.tab.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "title",
          "label": "t:global.text.title",
          "default": "Tab"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:global.text.content",
          "default": "<p>Use this text to share information about your brand with your customers. Describe a product, share announcements, or welcome customers to your store.</p>"
        },
        {
          "type": "page",
          "id": "page",
          "label": "t:sections.tabs.blocks.tab.page",
          "info": "t:sections.tabs.blocks.tab.page_info"
        },
        {
          "type": "checkbox",
          "id": "open_on_mobile",
          "label": "t:sections.tabs.blocks.tab.open_on_mobile",
          "info": "t:sections.tabs.blocks.tab.open_on_mobile_info",
          "default": false
        }
      ]
    },
    {
      "type": "liquid",
      "name": "t:sections.tabs.blocks.liquid.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "title",
          "label": "t:global.text.title",
          "default": "Tab"
        },
        {
          "type": "liquid",
          "id": "liquid",
          "label": "t:global.code.liquid",
          "default": "<p>Use Liquid code to create advanced customization.</p>"
        },
        {
          "type": "checkbox",
          "id": "open_on_mobile",
          "label": "t:sections.tabs.blocks.tab.open_on_mobile",
          "info": "t:sections.tabs.blocks.tab.open_on_mobile_info",
          "default": false
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.tabs.presets.tab.name",
      "blocks": [
        {
          "type": "tab",
          "settings": {
            "title": "Tab 1",
            "content": "<p>Use this text to share information about your brand with your customers.</p>"
          }
        },
        {
          "type": "tab",
          "settings": {
            "title": "Tab 2",
            "content": "<p>Describe a product, share announcements, or welcome customers to your store.</p>"
          }
        },
        {
          "type": "tab",
          "settings": {
            "title": "Tab 3",
            "content": "<p>Share information about your shipping rates, return policy or contact information.</p>"
          }
        }
      ]
    }
  ]
}
{% endschema %}