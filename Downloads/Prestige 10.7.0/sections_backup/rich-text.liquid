{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
HAINT-DEVELOPER NOTE: the slideshow is one of Prestige most complex sections due to the large number of settings it offers. We
highly recommend you to not edit the code unless you have fully reviewed and understood all the code. A simple change
may have important consequence on the section itself.
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

{%- if section.blocks.size > 0 -%}
  {%- assign color_scheme_hash = section.settings.color_scheme.settings.background_gradient | default: section.settings.color_scheme.settings.background | md5 -%}

  <div class="color-scheme color-scheme--{{ section.settings.color_scheme.id }} color-scheme--bg-{{ color_scheme_hash }} {% unless section.settings.remove_vertical_spacing %}section-spacing{% endunless %} {% if section.settings.separate_section_with_border %}bordered-section{% endif %}">
    <div class="container container--{{ section.settings.content_width }}">
      <div class="prose text-{{ section.settings.text_position }}">
        {%- assign is_inside_button_group_context = false -%}

        {%- for block in section.blocks -%}
          {%- if block.type == 'button' and is_inside_button_group_context == false -%}
            {%- assign is_inside_button_group_context = true -%}
            <div class="button-group justify-{{ section.settings.text_position }}">
          {%- elsif block.type != 'button' and is_inside_button_group_context -%}
            {%- assign is_inside_button_group_context = false -%}
            </div>
          {%- endif -%}

          {%- case block.type -%}
            {%- when 'subheading' -%}
              {%- if block.settings.text != blank -%}
                <p class="h6" {{ block.shopify_attributes }}>{{ block.settings.text }}</p>
              {%- endif -%}

            {%- when 'heading' -%}
              {%- if block.settings.text != blank -%}
                <p class="{{ block.settings.heading_tag }}" {{ block.shopify_attributes }}>{{ block.settings.text }}</p>
              {%- endif -%}

            {%- when 'richtext' -%}
              {%- if block.settings.content != blank -%}
                <div {{ block.shopify_attributes }}>
                  {{- block.settings.content -}}
                </div>
              {%- endif -%}

            {%- when 'page' -%}
              {%- if block.settings.page.content != blank -%}
                <div {{ block.shopify_attributes }}>
                  {{- block.settings.page.content -}}
                </div>
              {%- endif -%}

            {%- when 'image' -%}
              {%- if block.settings.image != blank -%}
                <div {{ block.shopify_attributes }}>
                  {%- capture image_style_attribute -%}{% if section.settings.text_position == 'center' %}margin-inline: auto;{% elsif section.settings.text_position == 'end' %}margin-inline-start: auto;{% endif %}{%- endcapture -%}

                  {%- if block.settings.width_mode == 'custom' -%}
                    {%- capture image_style_attribute -%}{{ image_style_attribute }} --image-max-width: {{ block.settings.max_width }}px; --image-mobile-max-width: {{ block.settings.mobile_max_width }}px{%- endcapture -%}
                    {%- capture image_sizes_attribute -%}(max-width: 699px) min({{ block.settings.mobile_max_width }}px, 100vw), min({{ block.settings.max_width }}px, 100vw){%- endcapture -%}
                    {%- capture image_widths -%}{{ block.settings.mobile_max_width }},{{ block.settings.mobile_max_width | times: 2 }},{{ block.settings.max_width }},{{ block.settings.max_width | times: 2 }}{%- endcapture -%}
                  {%- else -%}
                    {%- capture image_sizes_attribute -%}min(100vw, var(--page-width)){%- endcapture -%}
                    {%- assign image_widths = '200,300,400,500,600,800,1000,1200,1400,1600' -%}
                  {%- endif -%}

                  {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: sizes: image_sizes_attribute, widths: image_widths, class: 'constrained-image', style: image_style_attribute -}}
                </div>
              {%- endif -%}

            {%- when 'video' -%}
              {%- if block.settings.video != blank -%}
                <div {{ block.shopify_attributes }}>
                  {%- render 'media', media: block.settings.video, controls: true -%}
                </div>
              {%- endif -%}

            {%- when 'link' -%}
              <a {% if block.settings.url %}href="{{ block.settings.url }}"{% endif %} class="link" {{ block.shopify_attributes }}>{{ block.settings.text | escape }}</a>

            {%- when 'button' -%}
              {%- if block.settings.text != blank -%}
                {% render 'button', content: block.settings.text, href: block.settings.url, size: block.settings.size, style: block.settings.style, background: block.settings.background, text_color: block.settings.text_color, block: block %}
              {%- endif -%}

            {%- when 'liquid' -%}
              {%- if block.settings.liquid != blank -%}
                <div {{ block.shopify_attributes }}>
                  {{- block.settings.liquid -}}
                </div>
              {%- endif -%}
          {%- endcase -%}
        {%- endfor -%}
      </div>
    </div>
  </div>
{%- endif -%}

{% schema %}
{
  "name": "t:sections.rich_text.name",
  "class": "shopify-section--rich-text",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "custom.overlay"]
  },
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:global.colors.scheme",
      "default": "scheme-1"
    },
    {
      "type": "checkbox",
      "id": "separate_section_with_border",
      "label": "t:global.section.separate_section_with_border",
      "default": true
    },
    {
      "type": "select",
      "id": "content_width",
      "label": "t:global.sizes.content_width",
      "options": [
        {
          "value": "xs",
          "label": "t:global.sizes.x_small"
        },
        {
          "value": "sm",
          "label": "t:global.sizes.small"
        },
        {
          "value": "md",
          "label": "t:global.sizes.medium"
        },
        {
          "value": "lg",
          "label": "t:global.sizes.large"
        }
      ],
      "default": "sm"
    },
    {
      "type": "select",
      "id": "text_position",
      "label": "t:global.position.content_position",
      "options": [
        {
          "value": "start",
          "label": "t:global.position.left"
        },
        {
          "value": "center",
          "label": "t:global.position.center"
        },
        {
          "value": "end",
          "label": "t:global.position.right"
        }
      ],
      "default": "center"
    },
    {
      "type": "checkbox",
      "id": "remove_vertical_spacing",
      "label": "t:global.spacing.remove_vertical_spacing",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "subheading",
      "name": "t:sections.rich_text.blocks.subheading.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "text",
          "label": "t:global.text.text",
          "default": "Subheading"
        }
      ]
    },
    {
      "type": "heading",
      "name": "t:sections.rich_text.blocks.heading.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "text",
          "label": "t:global.text.text",
          "default": "Heading"
        },
        {
          "type": "select",
          "id": "heading_tag",
          "label": "t:global.text.style",
          "options": [
            {
              "value": "h1",
              "label": "H1"
            },
            {
              "value": "h2",
              "label": "H2"
            },
            {
              "value": "h3",
              "label": "H3"
            },
            {
              "value": "h4",
              "label": "H4"
            },
            {
              "value": "h5",
              "label": "H5"
            },
            {
              "value": "h6",
              "label": "H6"
            }
          ],
          "default": "h1"
        }
      ]
    },
    {
      "type": "richtext",
      "name": "t:sections.rich_text.blocks.paragraph.name",
      "settings": [
        {
          "type": "richtext",
          "id": "content",
          "label": "t:global.text.content",
          "default": "<p>Use this text to share information about your brand with your customers. Describe a product, share announcements, or welcome customers to your store.</p>"
        }
      ]
    },
    {
      "type": "page",
      "name": "t:sections.rich_text.blocks.page.name",
      "settings": [
        {
          "type": "page",
          "id": "page",
          "label": "t:sections.rich_text.blocks.page.page"
        }
      ]
    },
    {
      "type": "link",
      "name": "t:sections.rich_text.blocks.link.name",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "label": "t:global.text.text",
          "default": "Text"
        },
        {
          "type": "url",
          "id": "url",
          "label": "t:global.text.link"
        }
      ]
    },
    {
      "type": "image",
      "name": "t:sections.rich_text.blocks.image.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:global.image.image",
          "info": "t:sections.rich_text.blocks.image.image_size_recommendation"
        },
        {
          "type": "radio",
          "id": "width_mode",
          "label": "t:global.image.width",
          "options": [
            {
              "value": "full_width",
              "label": "t:global.image.width_options.full_width"
            },
            {
              "value": "custom",
              "label": "t:global.image.width_options.custom"
            }
          ],
          "default": "custom"
        },
        {
          "type": "range",
          "id": "max_width",
          "min": 100,
          "max": 1000,
          "step": 10,
          "unit": "px",
          "label": "t:global.image.maximum_width",
          "default": 600
        },
        {
          "type": "range",
          "id": "mobile_max_width",
          "min": 100,
          "max": 600,
          "step": 10,
          "unit": "px",
          "label": "t:global.image.mobile_maximum_width",
          "default": 400
        }
      ]
    },
    {
      "type": "video",
      "name": "t:sections.rich_text.blocks.video.name",
      "settings": [
        {
          "type": "video",
          "id": "video",
          "label": "t:global.video.video"
        }
      ]
    },
    {
      "type": "button",
      "name": "t:sections.rich_text.blocks.button.name",
      "settings": [
        {
          "type": "select",
          "id": "style",
          "label": "t:global.text.style",
          "options": [
            {
              "value": "outline",
              "label": "t:global.text.button_style_options.outline"
            },
            {
              "value": "solid",
              "label": "t:global.text.button_style_options.solid"
            }
          ],
          "default": "solid"
        },
        {
          "type": "text",
          "id": "text",
          "label": "t:global.text.text",
          "default": "Button text"
        },
        {
          "type": "url",
          "id": "url",
          "label": "t:global.text.link"
        },
        {
          "type": "color",
          "id": "background",
          "label": "t:global.colors.background"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "t:global.text.text"
        }
      ]
    },
    {
      "type": "liquid",
      "name": "t:sections.rich_text.blocks.liquid.name",
      "settings": [
        {
          "type": "liquid",
          "id": "liquid",
          "label": "t:global.code.liquid",
          "default": "<p>Write custom Liquid code to include widget or dynamic code.</p>"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.rich_text.presets.rich_text.name",
      "blocks": [
        {
          "type": "subheading",
          "settings": {
            "text": "Subheading"
          }
        },
        {
          "type": "heading",
          "settings": {
            "text": "Heading",
            "heading_tag": "h1"
          }
        },
        {
          "type": "richtext"
        }
      ]
    }
  ]
}
{% endschema %}

{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
HAINT-DEVELOPER NOTE: the slideshow is one of Prestige most complex sections due to the large number of settings it offers. We
highly recommend you to not edit the code unless you have fully reviewed and understood all the code. A simple change
may have important consequence on the section itself.
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}
