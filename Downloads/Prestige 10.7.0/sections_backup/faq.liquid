{%- if section.blocks.size > 0 -%}
  {%- assign color_scheme_hash = section.settings.color_scheme.settings.background_gradient | default: section.settings.color_scheme.settings.background | md5 -%}

  <div class="section-spacing color-scheme color-scheme--{{ section.settings.color_scheme.id }} color-scheme--bg-{{ color_scheme_hash }} {% if section.settings.separate_section_with_border %}bordered-section{% endif %}">
    <div class="container container--md">
      <div class="section-stack">
        {%- render 'section-header', subheading: section.settings.subheading, heading: section.settings.title, content: section.settings.content -%}

        <div class="faq">
          {%- assign categories_blocks = section.blocks | where: 'type', 'category' -%}

          {%- if section.settings.show_categories and categories_blocks.size > 0 -%}
            <faq-toc class="faq__toc hidden md:grid">
              {%- for block in categories_blocks -%}
                <a class="faq__toc-item {% if forloop.first %}is-active{% endif %}" href="#block-{{ block.id }}">{{ block.settings.title }}</a>
              {%- endfor -%}
            </faq-toc>
          {%- endif -%}

          <div class="faq__content">
            {%- for block in section.blocks -%}
              {%- case block.type -%}
                {%- when 'category' -%}
                  {%- if block.settings.title != blank -%}
                    <p id="block-{{ block.id }}" class="faq__category h4" {{ block.shopify_attributes }}>
                    {%- if block.settings.custom_icon != blank -%}
                      {%- capture sizes -%}{{ block.settings.icon_width }}px{%- endcapture -%}
                      {%- capture widths -%}{{ block.settings.icon_width }}, {{ block.settings.icon_width | times: 2 | at_most: block.settings.custom_icon.width }}{%- endcapture -%}
                      {%- capture style -%}--image-max-width: {{ block.settings.icon_width }}px{%- endcapture -%}

                      {{- block.settings.custom_icon | image_url: width: block.settings.custom_icon.width | image_tag: sizes: sizes, widths: widths, class: 'constrained-image', style: style -}}
                    {%- elsif block.settings.icon != 'none' -%}
                      {%- render 'icon' with block.settings.icon, width: block.settings.icon_width -%}
                    {%- endif -%}

                      {{- block.settings.title -}}
                    </p>
                  {%- endif -%}

                {%- when 'question' -%}
                  {%- if block.settings.question != blank and block.settings.answer != blank -%}
                    {%- capture answer -%}
                      <div class="prose">
                        {{- block.settings.answer -}}
                      </div>
                    {%- endcapture -%}

                    {%- render 'accordion', title: block.settings.question, content: answer, show_title_as_text: true, block: block -%}
                  {%- endif -%}
              {%- endcase -%}
            {%- endfor -%}
          </div>
        </div>
      </div>
    </div>
  </div>

  {%- comment -%}We also output the content with JSON microdata for SEO{%- endcomment -%}

  <script type="application/ld+json">
    {%- assign question_blocks = section.blocks | where: 'type', 'question' -%}

    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {%- for block in question_blocks -%}
          {
            "@type": "Question",
            "name": {{ block.settings.question | json }},
            "acceptedAnswer": {
              "@type": "Answer",
              "text": {{ block.settings.answer | json }}
            }
          }{% unless forloop.last %},{% endunless %}
        {%- endfor -%}
      ]
    }
  </script>
{%- endif -%}

{% schema %}
{
  "name": "t:sections.faq.name",
  "class": "shopify-section--faq",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "custom.overlay"]
  },
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:global.colors.scheme",
      "default": "scheme-1"
    },
    {
      "type": "checkbox",
      "id": "separate_section_with_border",
      "label": "t:global.section.separate_section_with_border",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_categories",
      "label": "t:sections.faq.show_categories"
    },
    {
      "type": "header",
      "content": "t:global.section.header_category"
    },
    {
      "type": "inline_richtext",
      "id": "subheading",
      "label": "t:global.text.subheading",
      "default": "Need help?"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "label": "t:global.text.heading",
      "default": "Frequently Asked Questions"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "t:global.text.content"
    }
  ],
  "blocks": [
    {
      "type": "category",
      "name": "t:sections.faq.blocks.category.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "title",
          "label": "t:global.text.title",
          "default": "Category"
        },
        {
          "type": "select",
          "id": "icon",
          "label": "t:global.icons.icon",
          "options": [
            {
              "value": "none",
              "label": "t:global.icons.none"
            },
            {
              "value": "picto-award-gift",
              "label": "t:global.icons.award_gift",
              "group": "t:global.icons.shop_category"
            },
            {
              "value": "picto-bag-handle",
              "label": "t:global.icons.bag_handle",
              "group": "t:global.icons.shop_category"
            },
            {
              "value": "picto-building",
              "label": "t:global.icons.building",
              "group": "t:global.icons.shop_category"
            },
            {
              "value": "picto-coupon",
              "label": "t:global.icons.coupon",
              "group": "t:global.icons.shop_category"
            },
            {
              "value": "picto-gift",
              "label": "t:global.icons.gift",
              "group": "t:global.icons.shop_category"
            },
            {
              "value": "picto-info",
              "label": "t:global.icons.info",
              "group": "t:global.icons.shop_category"
            },
            {
              "value": "picto-love",
              "label": "t:global.icons.love",
              "group": "t:global.icons.shop_category"
            },
            {
              "value": "picto-percent",
              "label": "t:global.icons.percent",
              "group": "t:global.icons.shop_category"
            },
            {
              "value": "picto-star",
              "label": "t:global.icons.star",
              "group": "t:global.icons.shop_category"
            },
            {
              "value": "picto-box",
              "label": "t:global.icons.box",
              "group": "t:global.icons.shipping_category"
            },
            {
              "value": "picto-delivery-truck",
              "label": "t:global.icons.delivery_truck",
              "group": "t:global.icons.shipping_category"
            },
            {
              "value": "picto-pin",
              "label": "t:global.icons.pin",
              "group": "t:global.icons.shipping_category"
            },
            {
              "value": "picto-plane",
              "label": "t:global.icons.plane",
              "group": "t:global.icons.shipping_category"
            },
            {
              "value": "picto-return",
              "label": "t:global.icons.return",
              "group": "t:global.icons.shipping_category"
            },
            {
              "value": "picto-credit-card",
              "label": "t:global.icons.credit_card",
              "group": "t:global.icons.payment_and_security_category"
            },
            {
              "value": "picto-lock",
              "label": "t:global.icons.lock",
              "group": "t:global.icons.payment_and_security_category"
            },
            {
              "value": "picto-money",
              "label": "t:global.icons.money",
              "group": "t:global.icons.payment_and_security_category"
            },
            {
              "value": "picto-secure-profile",
              "label": "t:global.icons.secure_profile",
              "group": "t:global.icons.payment_and_security_category"
            },
            {
              "value": "picto-shield",
              "label": "t:global.icons.shield",
              "group": "t:global.icons.payment_and_security_category"
            },
            {
              "value": "picto-earth",
              "label": "t:global.icons.earth",
              "group": "t:global.icons.ecology_category"
            },
            {
              "value": "picto-leaf",
              "label": "t:global.icons.leaf",
              "group": "t:global.icons.ecology_category"
            },
            {
              "value": "picto-recycle",
              "label": "t:global.icons.recycle",
              "group": "t:global.icons.ecology_category"
            },
            {
              "value": "picto-tree",
              "label": "t:global.icons.tree",
              "group": "t:global.icons.ecology_category"
            },
            {
              "value": "picto-at-sign",
              "label": "t:global.icons.at_sign",
              "group": "t:global.icons.tech_category"
            },
            {
              "value": "picto-bluetooth",
              "label": "t:global.icons.bluetooth",
              "group": "t:global.icons.tech_category"
            },
            {
              "value": "picto-camera",
              "label": "t:global.icons.camera",
              "group": "t:global.icons.tech_category"
            },
            {
              "value": "picto-printer",
              "label": "t:global.icons.printer",
              "group": "t:global.icons.tech_category"
            },
            {
              "value": "picto-smart-watch",
              "label": "t:global.icons.smart_watch",
              "group": "t:global.icons.tech_category"
            },
            {
              "value": "picto-wifi",
              "label": "t:global.icons.wifi",
              "group": "t:global.icons.tech_category"
            },
            {
              "value": "picto-avatar",
              "label": "t:global.icons.avatar",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-chat",
              "label": "t:global.icons.chat",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-calendar",
              "label": "t:global.icons.calendar",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-comment",
              "label": "t:global.icons.comment",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-customer-support",
              "label": "t:global.icons.customer_support",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-happy-face",
              "label": "t:global.icons.happy_face",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-mailbox",
              "label": "t:global.icons.mailbox",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-mobile-phone",
              "label": "t:global.icons.mobile_phone",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-operator",
              "label": "t:global.icons.operator",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-phone",
              "label": "t:global.icons.phone",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-send",
              "label": "t:global.icons.send",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-user",
              "label": "t:global.icons.user",
              "group": "t:global.icons.communication_category"
            },
            {
              "value": "picto-burger",
              "label": "t:global.icons.burger",
              "group": "t:global.icons.food_category"
            },
            {
              "value": "picto-beer",
              "label": "t:global.icons.beer",
              "group": "t:global.icons.food_category"
            },
            {
              "value": "picto-coffee",
              "label": "t:global.icons.coffee",
              "group": "t:global.icons.food_category"
            },
            {
              "value": "picto-pizza",
              "label": "t:global.icons.pizza",
              "group": "t:global.icons.food_category"
            },
            {
              "value": "picto-bottle",
              "label": "t:global.icons.bottle",
              "group": "t:global.icons.food_category"
            },
            {
              "value": "picto-document",
              "label": "t:global.icons.document",
              "group": "t:global.icons.other_category"
            },
            {
              "value": "picto-error",
              "label": "t:global.icons.error",
              "group": "t:global.icons.other_category"
            },
            {
              "value": "picto-file",
              "label": "t:global.icons.file",
              "group": "t:global.icons.other_category"
            },
            {
              "value": "picto-jewelry",
              "label": "t:global.icons.jewelry",
              "group": "t:global.icons.other_category"
            },
            {
              "value": "picto-mask",
              "label": "t:global.icons.mask",
              "group": "t:global.icons.other_category"
            },
            {
              "value": "picto-music",
              "label": "t:global.icons.music",
              "group": "t:global.icons.other_category"
            },
            {
              "value": "picto-not-allowed",
              "label": "t:global.icons.not_allowed",
              "group": "t:global.icons.other_category"
            },
            {
              "value": "picto-target",
              "label": "t:global.icons.target",
              "group": "t:global.icons.other_category"
            },
            {
              "value": "picto-timer",
              "label": "t:global.icons.timer",
              "group": "t:global.icons.other_category"
            },
            {
              "value": "picto-success",
              "label": "t:global.icons.success",
              "group": "t:global.icons.other_category"
            }
          ],
          "default": "none"
        },
        {
          "type": "image_picker",
          "id": "custom_icon",
          "label": "t:global.icons.custom_icon",
          "info": "t:global.icons.custom_icon_info"
        },
        {
          "type": "range",
          "id": "icon_width",
          "min": 12,
          "max": 32,
          "step": 4,
          "unit": "px",
          "label": "t:global.icons.icon_width",
          "default": 16
        }
      ]
    },
    {
      "type": "question",
      "name": "t:sections.faq.blocks.question.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "question",
          "label": "t:sections.faq.blocks.question.question",
          "default": "Question"
        },
        {
          "type": "richtext",
          "id": "answer",
          "label": "t:sections.faq.blocks.question.answer",
          "default": "<p>Write a clear and concise answer to guide your customers.</p>"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.faq.presets.faq.name",
      "blocks": [
        {
          "type": "category",
          "settings": {
            "title": "Order"
          }
        },
        {
          "type": "question",
          "settings": {
            "question": "Do you ship overseas?",
            "answer": "<p>Yes, we ship all over the world. Shipping costs will apply, and will be added at checkout. We run discounts and promotions all year, so stay tuned for exclusive deals.</p>"
          }
        },
        {
          "type": "question",
          "settings": {
            "question": "How long will it take to get my orders?",
            "answer": "<p>It depends on where you are. Orders processed here will take 5-7 business days to arrive. Overseas deliveries can take anywhere from 7-16 days. Delivery details will be provided in your confirmation email.</p>"
          }
        },
        {
          "type": "question",
          "settings": {
            "question": "Any question?",
            "answer": "<p>You can contact us through our contact page! We will be happy to assist you.</p>"
          }
        }
      ]
    }
  ]
}
{% endschema %}