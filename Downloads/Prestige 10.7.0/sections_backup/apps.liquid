{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
HAINT-DEVELOPER NOTE: the slideshow is one of Prestige most complex sections due to the large number of settings it offers. We
highly recommend you to not edit the code unless you have fully reviewed and understood all the code. A simple change
may have important consequence on the section itself.
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

{%- if section.blocks.size > 0 -%}
  {%- assign color_scheme_hash = section.settings.color_scheme.settings.background_gradient | default: section.settings.color_scheme.settings.background | md5 -%}

  <div class="color-scheme color-scheme--{{ section.settings.color_scheme.id }} color-scheme--bg-{{ color_scheme_hash }} {% unless section.settings.remove_vertical_spacing %}section-spacing{% endunless %} {% if section.settings.separate_section_with_border %}bordered-section{% endif %}">
    <div {% unless section.settings.remove_horizontal_spacing %}class="container"{% endunless %}>
      {%- for block in section.blocks -%}
        {%- render block -%}
      {%- endfor -%}
    </div>
  </div>
{%- endif -%}

{% schema %}
{
  "name": "t:sections.apps.name",
  "class": "shopify-section--apps",
  "tag": "section",
  "disabled_on": {
    "groups": ["custom.overlay"]
  },
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:global.colors.scheme",
      "default": "scheme-1"
    },
    {
      "type": "checkbox",
      "id": "separate_section_with_border",
      "label": "t:global.section.separate_section_with_border",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "remove_vertical_spacing",
      "label": "t:global.spacing.remove_vertical_spacing",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "remove_horizontal_spacing",
      "label": "t:global.spacing.remove_horizontal_spacing",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "@app"
    }
  ],
  "presets": [
    {
      "name": "t:sections.apps.presets.apps.name"
    }
  ]
}
{% endschema %}

{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
HAINT-DEVELOPER NOTE: the slideshow is one of Prestige most complex sections due to the large number of settings it offers. We
highly recommend you to not edit the code unless you have fully reviewed and understood all the code. A simple change
may have important consequence on the section itself.
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}
