{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
HAINT-DEVELOPER NOTE: the slideshow is one of Prestige most complex sections due to the large number of settings it offers. We
highly recommend you to not edit the code unless you have fully reviewed and understood all the code. A simple change
may have important consequence on the section itself.
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

<style>
  #shopify-section-{{ section.id }} {
    --footer-content-justify-items: {% if section.settings.blocks.size <= 2 %}start{% else %}space-between{% endif %};
  }
</style>

{%- assign color_scheme_hash = section.settings.color_scheme.settings.background_gradient | default: section.settings.color_scheme.settings.background | md5 -%}

<div class="footer color-scheme color-scheme--{{ section.settings.color_scheme.id }} color-scheme--bg-{{ color_scheme_hash }} {% if section.settings.separate_section_with_border %}bordered-section{% endif %}">
  <div class="container">
    <div class="footer__inner">
      {%- comment -%}
      ------------------------------------------------------------------------------------------------------------------
      FOOTER BLOCKS (MAIN CONTENT)
      ------------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}

      {%- if section.blocks.size > 0 -%}
        <div class="footer__block-list">
          {%- for block in section.blocks -%}
            {%- capture block_content -%}
              {%- case block.type -%}
                {%- when '@app' -%}
                  {%- render block -%}

                {%- when 'image' -%}
                  {%- if block.settings.image != blank -%}
                    {%- capture sizes -%}{{ block.settings.image_width }}px{%- endcapture -%}
                    {%- capture widths -%}{{ block.settings.image_width }}, {{ block.settings.image_width | times: 2 | at_most: block.settings.image.width }}{%- endcapture -%}
                    {%- capture style -%}--image-max-width: {{ block.settings.image_width }}px{%- endcapture -%}

                    {%- if block.settings.link != blank -%}
                      <a href="{{ block.settings.link }}">
                        {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: sizes: sizes, widths: widths, style: style, class: 'constrained-image' -}}
                      </a>
                    {%- else -%}
                      {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: sizes: sizes, widths: widths, style: style, class: 'constrained-image' -}}
                    {%- endif -%}
                  {%- endif -%}

                {%- when 'text' -%}
                  {%- if block.settings.title != blank or block.settings.content != blank or shop.features.follow_on_shop? and block.settings.enable_follow_on_shop -%}
                    <div class="v-stack gap-4 sm:gap-5">
                      {%- if block.settings.title != blank -%}
                        <p class="h6">{{ block.settings.title }}</p>
                      {%- endif -%}

                      {%- if block.settings.content != blank -%}
                        <div class="prose text-subdued">
                          {{- block.settings.content -}}
                        </div>
                      {%- endif -%}

                      {%- if shop.features.follow_on_shop? and block.settings.enable_follow_on_shop -%}
                        <div class="follow-on-shop">
                          {{- shop | login_button: action: 'follow' -}}
                        </div>
                      {%- endif -%}
                    </div>
                  {%- endif -%}

                {%- when 'links' -%}
                  {%- assign menu = block.settings.menu -%}

                  {%- if menu != blank and menu.links.size > 0 -%}
                    <div class="v-stack gap-4 sm:gap-5">
                      {%- if block.settings.show_menu_title != blank -%}
                        <p class="h6">{{ block.settings.menu_title | default: menu.title }}</p>
                      {%- endif -%}

                      <ul class="v-stack gap-2.5 unstyled-list" role="list">
                        {%- for link in menu.links -%}
                          <li>
                            <a href="{{ link.url }}" class="link-faded">{{ link.title }}</a>
                          </li>
                        {%- endfor -%}
                      </ul>
                    </div>
                  {%- endif -%}

                {%- when 'newsletter' -%}
                  <div class="v-stack gap-4 sm:gap-5">
                    {%- if block.settings.title != blank -%}
                      <p class="h6">{{ block.settings.title }}</p>
                    {%- endif -%}

                    {%- if block.settings.content != blank -%}
                      <div class="prose text-subdued">
                        {{- block.settings.content -}}
                      </div>
                    {%- endif -%}

                    {%- assign newsletter_form_id = 'newsletter-form-' | append: section.id -%}

                    {%- form 'customer', id: newsletter_form_id, class: 'form' -%}
                      {%- if form.posted_successfully? -%}
                        {%- assign success_message = 'general.newsletter.subscribed_successfully' | t -%}
                        {%- render 'banner', content: success_message, status: 'success' -%}
                      {%- else -%}
                        {%- if form.errors -%}
                          {%- capture error_message -%}{{ form.errors.translated_fields['email'] }} {{ form.errors.messages['email'] }}{%- endcapture -%}
                          {%- render 'banner', content: error_message, status: 'error' -%}
                        {%- endif -%}

                        <input type="hidden" name="contact[tags]" value="newsletter">

                        {%- assign input_label = 'general.newsletter.email' | t -%}
                        {%- render 'input', name: 'contact[email]', label: input_label, type: 'email', required: true, autocomplete: 'email', enterkeyhint: 'send' -%}

                        <div class="align-self-start">
                          {%- assign button_label = 'general.newsletter.subscribe' | t -%}
                          {%- render 'button', type: 'submit', content: button_label, background: block.settings.button_background, text_color: block.settings.button_text_color -%}
                        </div>
                      {%- endif -%}
                    {%- endform -%}
                  </div>

              {%- endcase -%}
            {%- endcapture -%}

            {%- if block_content != blank -%}
              <div class="footer__block footer__block--{{ block.type | remove_first: '@' }}" {{ block.shopify_attributes }}>
                {{- block_content -}}
              </div>
            {%- endif -%}
          {%- endfor -%}
        </div>
      {%- endif -%}

      {%- comment -%}
      ------------------------------------------------------------------------------------------------------------------
      FOOTER SOCIAL MEDIA
      ------------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}

      {%- if section.settings.show_social_media -%}
        {%- render 'social-media', layout: 'list' -%}
      {%- endif -%}

      {%- comment -%}
      ------------------------------------------------------------------------------------------------------------------
      FOOTER ASIDE (COPYRIGHT + LOCALIZATION SELECTORS + PAYMENT METHODS)
      ------------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}

      <div class="footer__aside">
        {%- if section.settings.show_country_selector and localization.available_countries.size > 1 -%}
          {%- assign show_country_selector = true -%}
        {%- endif -%}

        {%- if section.settings.show_locale_selector and localization.available_languages.size > 1 -%}
          {%- assign show_locale_selector = true -%}
        {%- endif -%}

        {%- if show_country_selector or show_locale_selector -%}
          <div class="localization-selectors">
            {%- if show_country_selector -%}
              {%- render 'localization-selector', type: 'country', placement: 'top-start', show_country_name: section.settings.show_country_name, show_country_flag: section.settings.show_country_flag -%}
            {%- endif -%}

            {%- if show_locale_selector -%}
              {%- render 'localization-selector', type: 'locale', placement: 'top-start' -%}
            {%- endif -%}
          </div>
        {%- endif -%}

        <p class="heading text-subdued text-xxs">© {{ 'now' | date: '%Y' }} - {{ shop.name }} {{ powered_by_link }}</p>

        {%- if section.settings.show_payment_icons and shop.enabled_payment_types.size > 0 -%}
          <ul class="payment-methods unstyled-list">
            {%- for type in shop.enabled_payment_types -%}
              <li>{{- type | payment_type_svg_tag -}}</li>
            {%- endfor -%}
          </ul>
        {%- endif -%}
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.footer.name",
  "class": "shopify-section--footer",
  "tag": "footer",
  "max_blocks": 6,
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:global.colors.scheme",
      "default": "scheme-2"
    },
    {
      "type": "checkbox",
      "id": "separate_section_with_border",
      "label": "t:global.section.separate_section_with_border",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_social_media",
      "label": "t:sections.footer.show_social_media",
      "info": "t:sections.footer.show_social_media_info",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_payment_icons",
      "label": "t:sections.footer.show_payment_icons",
      "default": true
    },
    {
      "type": "header",
      "content": "t:global.localization.country_region_selector_category",
      "info": "t:global.localization.country_region_selector_category_info"
    },
    {
      "type": "checkbox",
      "id": "show_country_selector",
      "label": "t:global.localization.show_country_region_selector",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_country_name",
      "label": "t:global.localization.show_country_name",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_country_flag",
      "label": "t:global.localization.show_country_flag",
      "default": true
    },
    {
      "type": "header",
      "content": "t:global.localization.language_selector_category",
      "info": "t:global.localization.language_selector_category_info"
    },
    {
      "type": "checkbox",
      "id": "show_locale_selector",
      "label": "t:global.localization.show_locale_selector",
      "default": true
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "image",
      "name": "t:sections.footer.blocks.image.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:global.image.image",
          "info": "t:sections.footer.blocks.image.image_size_recommendation"
        },
        {
          "type": "range",
          "id": "image_width",
          "min": 50,
          "max": 300,
          "step": 10,
          "unit": "px",
          "label": "t:global.image.width",
          "default": 150
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:global.text.link"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.footer.blocks.text.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "title",
          "label": "t:global.text.heading",
          "default": "About our store"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:global.text.content",
          "default": "<p>Use this text area to tell your customers about your brand and vision. You can change it in the theme editor.</p>"
        },
        {
          "type": "checkbox",
          "id": "enable_follow_on_shop",
          "label": "t:sections.footer.blocks.text.show_follow_on_shop",
          "info": "t:sections.footer.blocks.text.show_follow_on_shop_info",
          "default": true
        }
      ]
    },
    {
      "type": "links",
      "name": "t:sections.footer.blocks.links.name",
      "settings": [
        {
          "type": "link_list",
          "id": "menu",
          "label": "t:sections.footer.blocks.links.menu",
          "info": "t:sections.footer.blocks.links.menu_info",
          "default": "footer"
        },
        {
          "type": "checkbox",
          "id": "show_menu_title",
          "label": "t:sections.footer.blocks.links.show_menu_title",
          "default": true
        },
        {
          "type": "inline_richtext",
          "id": "menu_title",
          "label": "t:sections.footer.blocks.links.menu_title",
          "info": "t:sections.footer.blocks.links.menu_title_info"
        }
      ]
    },
    {
      "type": "newsletter",
      "name": "t:sections.footer.blocks.newsletter.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.footer.blocks.newsletter.newsletter_info"
        },
        {
          "type": "inline_richtext",
          "id": "title",
          "label": "t:global.text.heading",
          "default": "Newsletter"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:global.text.content",
          "default": "<p>Sign up to our newsletter to receive exclusive offers.</p>"
        },
        {
          "type": "color",
          "id": "button_background",
          "label": "t:global.colors.button_background"
        },
        {
          "type": "color",
          "id": "button_text_color",
          "label": "t:global.colors.button_text"
        }
      ]
    }
  ]
}
{% endschema %}
{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
HAINT-DEVELOPER NOTE: the slideshow is one of Prestige most complex sections due to the large number of settings it offers. We
highly recommend you to not edit the code unless you have fully reviewed and understood all the code. A simple change
may have important consequence on the section itself.
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}
