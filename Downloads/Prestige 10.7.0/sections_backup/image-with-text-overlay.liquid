{%- if section.settings.image != blank or section.blocks.size > 0 -%}
  {%- comment -%}
  ------------------------------------------------------------------------------------------------------------------------
  CSS
  ------------------------------------------------------------------------------------------------------------------------
  {%- endcomment -%}

  <style>
    #shopify-section-{{ section.id }} {
      {%- if section.settings.overlay_color != 'rgba(0,0,0,0)' -%}
        --content-over-media-overlay: {{ section.settings.overlay_color.rgb }} / {{ section.settings.overlay_opacity | divided_by: 100.0 }};
      {%- endif -%}

      --content-over-media-content-max-width: var(--container-{{ section.settings.content_width }}-max-width);
    }
  </style>

  {%- comment -%}
  ------------------------------------------------------------------------------------------------------------------------
  LIQUID
  ------------------------------------------------------------------------------------------------------------------------
  {%- endcomment -%}

  <div class="{% unless section.settings.remove_vertical_spacing %}section-spacing{% endunless %} color-scheme color-scheme--{{ section.settings.color_scheme.id }} color-scheme--with-image-overlay" {% if section.settings.allow_transparent_header %}allow-transparent-header{% endif %}>
    <image-with-text-overlay class="content-over-media content-over-media--{{ section.settings.image_size }}" reveal-on-scroll="{{ section.settings.reveal_on_scroll }}">
      {%- if section.settings.image != blank -%}
        <picture>
          {%- if section.settings.mobile_image != blank -%}
            <source
                media="(max-width: 699px)"
                srcset="{{ section.settings.mobile_image | image_url: width: 400 }} 400w, {{ section.settings.mobile_image | image_url: width: 600 }} 600w, {{ section.settings.mobile_image | image_url: width: 800 }} 800w, {{ section.settings.mobile_image | image_url: width: 1000 }} 1000w"
                width="{{ section.settings.mobile_image.width }}"
                height="{{ section.settings.mobile_image.height }}"
            >
          {%- endif -%}

          {{- section.settings.image | image_url: width: section.settings.image.width | image_tag: sizes: '100vw', widths: '200,300,400,500,600,700,800,900,1000,1200,1400,1600,1800,2000,2200,2400,2600,2800,3000,3200' -}}
        </picture>
      {%- else -%}
        {{- 'lifestyle-1' | placeholder_svg_tag: 'placeholder' | replace: '<svg', '<svg preserveAspectRatio="xMinYMin slice"' -}}
      {%- endif -%}

      {%- if section.blocks.size > 0 -%}
        <div class="content {{ section.settings.mobile_text_position }} {{ section.settings.desktop_text_position }}">
          <div class="prose">
            {%- for block in section.blocks -%}
              {%- case block.type -%}
                {%- when 'subheading' -%}
                  {%- if block.settings.text != blank -%}
                    <p class="h6" {{ block.shopify_attributes }}>{{ block.settings.text }}</p>
                  {%- endif -%}

                {%- when 'heading' -%}
                  {%- if block.settings.text != blank -%}
                    <p class="{{ block.settings.heading_tag }}" {{ block.shopify_attributes }}>{{ block.settings.text }}</p>
                  {%- endif -%}

                {%- when 'richtext' -%}
                  {%- if block.settings.content != blank -%}
                    <div {{ block.shopify_attributes }}>
                      {{- block.settings.content -}}
                    </div>
                  {%- endif -%}

                {%- when 'liquid' -%}
                  {%- if block.settings.liquid != blank -%}
                    <div {{ block.shopify_attributes }}>
                      {{- block.settings.liquid -}}
                    </div>
                  {%- endif -%}

                {%- when 'button' -%}
                  {%- if block.settings.text != blank -%}
                    {%- render 'button', content: block.settings.text, href: block.settings.url, style: block.settings.style, background: block.settings.background, text_color: block.settings.text_color, block: block -%}
                  {%- endif -%}

                {%- when 'link' -%}
                  {%- if block.settings.text != blank -%}
                    <div {{ block.shopify_attributes }}>
                      <a href="{{ block.settings.url }}">{{ block.settings.text }}</a>
                    </div>
                  {%- endif -%}
              {%- endcase -%}
            {%- endfor -%}
          </div>
        </div>
      {%- endif -%}
    </image-with-text-overlay>
  </div>
{%- endif -%}

{% schema %}
{
  "name": "t:sections.image_with_text_overlay.name",
  "class": "shopify-section--image-with-text-overlay",
  "tag": "section",
  "max_blocks": 6,
  "disabled_on": {
    "groups": ["header", "custom.overlay"]
  },
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:global.colors.scheme",
      "default": "scheme-4"
    },
    {
      "type": "select",
      "id": "image_size",
      "label": "t:global.image.size",
      "options": [
        {
          "value": "auto",
          "label": "t:global.sizes.original_image_ratio"
        },
        {
          "value": "xs",
          "label": "t:global.sizes.x_small"
        },
        {
          "value": "sm",
          "label": "t:global.sizes.small"
        },
        {
          "value": "md",
          "label": "t:global.sizes.medium"
        },
        {
          "value": "lg",
          "label": "t:global.sizes.large"
        },
        {
          "value": "fill",
          "label": "t:global.sizes.fit_screen"
        }
      ],
      "info": "t:global.image.ratio_avoid_cropping_info",
      "default": "auto"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:global.image.image",
      "info": "t:sections.image_with_text_overlay.image_size_recommendation"
    },
    {
      "type": "image_picker",
      "id": "mobile_image",
      "label": "t:global.image.mobile_image",
      "info": "t:sections.image_with_text_overlay.mobile_image_size_recommendation"
    },
    {
      "type": "checkbox",
      "id": "allow_transparent_header",
      "label": "t:global.section.allow_transparent_header",
      "info": "t:global.section.allow_transparent_header_info",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "reveal_on_scroll",
      "label": "t:global.animation.reveal_image_on_scroll",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "remove_vertical_spacing",
      "label": "t:global.spacing.remove_vertical_spacing",
      "default": true
    },
    {
      "type": "select",
      "id": "content_width",
      "label": "t:global.sizes.content_width",
      "options": [
        {
          "value": "xs",
          "label": "t:global.sizes.x_small"
        },
        {
          "value": "sm",
          "label": "t:global.sizes.small"
        },
        {
          "value": "md",
          "label": "t:global.sizes.medium"
        },
        {
          "value": "lg",
          "label": "t:global.sizes.large"
        },
        {
          "value": "xl",
          "label": "t:global.sizes.x_large"
        }
      ],
      "default": "md"
    },
    {
      "type": "select",
      "id": "mobile_text_position",
      "label": "t:global.position.mobile_content_position",
      "options": [
        {
          "value": "place-self-start text-start",
          "label": "t:global.position.top_left"
        },
        {
          "value": "place-self-start-center text-center",
          "label": "t:global.position.top_center"
        },
        {
          "value": "place-self-start-end text-end",
          "label": "t:global.position.top_right"
        },
        {
          "value": "place-self-center-start text-start",
          "label": "t:global.position.middle_left"
        },
        {
          "value": "place-self-center text-center",
          "label": "t:global.position.middle_center"
        },
        {
          "value": "place-self-center-end text-end",
          "label": "t:global.position.middle_right"
        },
        {
          "value": "place-self-end-start text-start",
          "label": "t:global.position.bottom_left"
        },
        {
          "value": "place-self-end-center text-center",
          "label": "t:global.position.bottom_center"
        },
        {
          "value": "place-self-end text-end",
          "label": "t:global.position.bottom_right"
        }
      ],
      "default": "place-self-center text-center"
    },
    {
      "type": "select",
      "id": "desktop_text_position",
      "label": "t:global.position.desktop_content_position",
      "options": [
        {
          "value": "sm:place-self-start sm:text-start",
          "label": "t:global.position.top_left"
        },
        {
          "value": "sm:place-self-start-center sm:text-center",
          "label": "t:global.position.top_center"
        },
        {
          "value": "sm:place-self-start-end sm:text-end",
          "label": "t:global.position.top_right"
        },
        {
          "value": "sm:place-self-center-start sm:text-start",
          "label": "t:global.position.middle_left"
        },
        {
          "value": "sm:place-self-center sm:text-center",
          "label": "t:global.position.middle_center"
        },
        {
          "value": "sm:place-self-center-end sm:text-end",
          "label": "t:global.position.middle_right"
        },
        {
          "value": "sm:place-self-end-start sm:text-start",
          "label": "t:global.position.bottom_left"
        },
        {
          "value": "sm:place-self-end-center sm:text-center",
          "label": "t:global.position.bottom_center"
        },
        {
          "value": "sm:place-self-end sm:text-end",
          "label": "t:global.position.bottom_right"
        }
      ],
      "default": "sm:place-self-center sm:text-center"
    },
    {
      "type": "header",
      "content": "t:global.colors.category"
    },
    {
      "type": "color",
      "id": "overlay_color",
      "label": "t:global.colors.overlay_color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "label": "t:global.colors.overlay_opacity",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "default": 30
    }
  ],
  "blocks": [
    {
      "type": "subheading",
      "name": "t:sections.image_with_text_overlay.blocks.subheading.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "text",
          "label": "t:global.text.text",
          "default": "Subheading"
        }
      ]
    },
    {
      "type": "heading",
      "name": "t:sections.image_with_text_overlay.blocks.heading.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "text",
          "label": "t:global.text.text",
          "default": "Heading"
        },
        {
          "type": "select",
          "id": "heading_tag",
          "label": "t:global.text.style",
          "options": [
            {
              "value": "h1",
              "label": "H1"
            },
            {
              "value": "h2",
              "label": "H2"
            },
            {
              "value": "h3",
              "label": "H3"
            },
            {
              "value": "h4",
              "label": "H4"
            },
            {
              "value": "h5",
              "label": "H5"
            },
            {
              "value": "h6",
              "label": "H6"
            }
          ],
          "default": "h4"
        }
      ]
    },
    {
      "type": "richtext",
      "name": "t:sections.image_with_text_overlay.blocks.paragraph.name",
      "settings": [
        {
          "type": "richtext",
          "id": "content",
          "label": "t:global.text.content",
          "default": "<p>Use this text to share information about your brand with your customers. Describe a product, share announcements, or welcome customers to your store.</p>"
        }
      ]
    },
    {
      "type": "liquid",
      "name": "t:sections.image_with_text_overlay.blocks.liquid.name",
      "settings": [
        {
          "type": "liquid",
          "id": "liquid",
          "label": "t:global.code.liquid",
          "default": "<p>Use this text to output some custom Liquid code to include widget or dynamic code.</p>"
        }
      ]
    },
    {
      "type": "button",
      "name": "t:sections.image_with_text_overlay.blocks.button.name",
      "settings": [
        {
          "type": "select",
          "id": "style",
          "label": "t:global.text.style",
          "options": [
            {
              "value": "outline",
              "label": "t:global.text.button_style_options.outline"
            },
            {
              "value": "solid",
              "label": "t:global.text.button_style_options.solid"
            }
          ],
          "default": "solid"
        },
        {
          "type": "text",
          "id": "text",
          "label": "t:global.text.text",
          "default": "Button text"
        },
        {
          "type": "url",
          "id": "url",
          "label": "t:global.text.link"
        },
        {
          "type": "color",
          "id": "background",
          "label": "t:global.colors.background"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "t:global.colors.text"
        }
      ]
    },
    {
      "type": "link",
      "name": "t:sections.image_with_text_overlay.blocks.link.name",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "label": "t:global.text.text",
          "default": "Link text"
        },
        {
          "type": "url",
          "id": "url",
          "label": "t:global.text.link"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.image_with_text_overlay.presets.image_with_text_overlay.name",
      "blocks": [
       {
          "type": "heading",
          "settings": {
            "text": "Image with text overlay"
          }
        },
        {
          "type": "richtext",
          "settings": {
            "content": "<p>Add your own custom content to give more information about your store, availability details...</p>"
          }
        }
      ]
    }
  ]
}
{% endschema %}
