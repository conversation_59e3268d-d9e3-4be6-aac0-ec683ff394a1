<style>
  #shopify-section-{{ section.id }} {
    --article-image-height: {% if section.settings.image_size == 'auto' %}auto{% elsif section.settings.image_size == 'small' %}min(300px, 30vmax){% elsif section.settings.image_size == 'medium' %}min(500px, 45vmax){% else %}min(700px, 60vmax){% endif %}
  }
</style>

{%- assign color_scheme_hash = section.settings.color_scheme.settings.background_gradient | default: section.settings.color_scheme.settings.background | md5 -%}

<div class="article color-scheme color-scheme--{{ section.settings.color_scheme.id }} color-scheme--bg-{{ color_scheme_hash }}">
  {%- if section.settings.show_sticky_bar -%}
    <article-toolbar class="article__toolbar color-scheme color-scheme--{{ section.settings.toolbar_color_scheme.id }} sm-max:hidden">
      <div class="container">
        <div class="h-stack justify-between">
          <p class="h-stack gap-1">
            <span class="text-subdued">{{ 'blog_post.article' | t }}</span>
            <span class="truncate-text" style="--truncate-text-max-width: 400px">{{ article.title }}</span>
          </p>

          <div class="h-stack gap-16">
            <div class="h-stack gap-5 hidden md:flex">
              <span class="text-subdued">{{ 'blog_post.share' | t }}</span>

              {%- if section.settings.show_share_buttons -%}
                {%- render 'share-buttons', layout: 'list', url: article.url, title: article.title, description: article.content -%}
              {%- endif -%}
            </div>

            <div class="h-stack gap-6">
              {%- if blog.previous_article -%}
                <a href="{{ blog.previous_article.url }}" class="h-stack gap-1 link-faded">
                  {%- render 'icon' with 'chevron-left' -%}
                  {{- 'blog_post.prev' | t -}}
                </a>
              {%- endif -%}

              {%- if blog.next_article -%}
                <a href="{{ blog.next_article.url }}" class="h-stack gap-1 link-faded">
                  {{- 'blog_post.next' | t -}}
                  {%- render 'icon' with 'chevron-right' -%}
                </a>
              {%- endif -%}
            </div>
          </div>
        </div>
      </div>
    </article-toolbar>
  {%- endif -%}

  {%- if section.settings.show_image and article.image -%}
    <div class="article__image">
      {%- if section.settings.enable_parallax -%}
        <image-parallax class="contents">
          {{- article.image | image_url: width: article.image.width | image_tag: widths: '200,300,400,500,600,700,800,900,1000,1200,1400,1600,1800,2000,2200,2400,2600,2800,3000,3200', class: 'w-full' -}}
        </image-parallax>
      {%- else -%}
        {{- article.image | image_url: width: article.image.width | image_tag: widths: '200,300,400,500,600,700,800,900,1000,1200,1400,1600,1800,2000,2200,2400,2600,2800,3000,3200', class: 'w-full' -}}
      {%- endif -%}
    </div>
  {%- endif -%}

  <div class="article__wrapper container--{{ section.settings.content_width }}">
    <div class="v-stack gap-10 sm:gap-16">
      <div class="v-stack gap-8 sm:gap-12">
        <div class="v-stack gap-4.5">
          {%- if section.settings.show_date or section.settings.show_category and article.tags.size > 0 -%}
            <div class="h-stack gap-4">
              {%- if section.settings.show_date -%}
                <span class="h6 text-subdued"><time>{{- article.published_at | date: format: 'abbreviated_date' -}}</time></span>
              {%- endif -%}

              {%- if section.settings.show_category and article.tags.size > 0 -%}
                {%- if section.settings.show_date -%}
                  <span class="separator-dot text-subdued"></span>
                {%- endif -%}

                <a href="{{ blog.url }}/tagged/{{ article.tags.first | handle }}" class="h6 link-faded">{{ article.tags.first }}</a>
              {%- endif -%}
            </div>
          {%- endif -%}

          <h1 class="h1">{{ article.title }}</h1>
        </div>

        <div class="prose">
          {{- article.content -}}
        </div>
      </div>

      {%- if section.settings.show_author or section.settings.show_share_buttons -%}
        {%- if section.settings.show_author and article.user.bio != blank or article.user.image != blank or article.user.homepage != blank -%}
          {%- assign has_extended_profile = true -%}
        {%- else -%}
          {%- assign has_extended_profile = false -%}
        {%- endif -%}

        <div class="article__footer">
          {%- if has_extended_profile -%}
            <div class="article__author">
              {%- if article.user.image != blank -%}
                {{- article.user.image | image_url: width: article.user.image.width | image_tag: loading: 'lazy', sizes: '60px', widths: '60,120,180', class: 'article__author-image' -}}
              {%- endif -%}

              <div class="v-stack gap-3 justify-items-start">
                <p class="h6">{{ 'blog.post.written_by' | t: author: article.author }}</p>

                {%- if article.user.bio != blank -%}
                  <div class="prose">
                    {{- article.user.bio -}}
                  </div>
                {%- endif -%}

                {%- if article.user.homepage != blank -%}
                  <a href="{{ article.user.homepage }}" class="link" target="_blank">{{ 'blog.post.visit_author_website' | t }}</a>
                {%- endif -%}
              </div>
            </div>
          {%- endif -%}

          <div class="article__footer-bottom">
            {%- if section.settings.show_author and has_extended_profile == false -%}
              <p class="h6 text-subdued">{{ 'blog.post.written_by' | t: author: article.author }}</p>
            {%- elsif section.settings.show_share_buttons -%}
              <p class="h6 text-subdued">{{ 'blog.post.share' | t }}</p>
            {%- endif -%}

            {%- if section.settings.show_share_buttons -%}
              {%- render 'share-buttons', layout: 'block', url: article.url, title: article.title, description: article.content -%}
            {%- endif -%}
          </div>
        </div>
      {%- endif -%}
    </div>

    {%- if article.comments_enabled? -%}
      <div id="comments" class="article__comments">
        {%- if article.comments_count > 0 -%}
          <div class="v-stack gap-8">
            <h3 class="h2">{{ 'blog.comments.count' | t: count: article.comments_count }}</h3>

            {%- paginate article.comments by 50 -%}
              <div class="v-stack gap-8 divide-y">
                {%- for comment in article.comments -%}
                  <article class="comment">
                    <div class="prose">
                      {{- comment.content -}}
                    </div>

                    <div class="h-stack gap-4.5">
                      <p class="text-xs text-subdued">{{ comment.author }}</p>
                      <time class="text-xs text-subdued">{{ comment.created_at | date: format: 'date_at_time' }}</time>
                    </div>
                  </article>
                {%- endfor -%}
              </div>

              {%- render 'pagination', paginate: paginate, hash: '#comments' -%}
            {%- endpaginate -%}
          </div>
        {%- endif -%}

        <div class="v-stack gap-8">
          <div class="v-stack gap-4">
            <h3 class="h2">{{ 'blog.comments.leave_comment' | t }}</h3>

            <div class="prose">
              {{- 'shopify.online_store.spam_detection.disclaimer_html' | t -}}
            </div>
          </div>

          {%- form 'new_comment', article, class: 'form' -%}
            {%- if form.posted_successfully? -%}
              {%- capture content -%}
                {%- if blog.moderated? -%}
                  {{- 'blog.comments.comment_sent' | t -}}
                {%- else -%}
                  {{- 'blog.comments.comment_published' | t -}}
                {%- endif -%}
              {%- endcapture -%}

              {%- render 'banner', status: 'success', content: content -%}

            {%- else -%}
              {%- if form.errors -%}
                {%- assign content = form.errors | default_errors -%}
                {%- render 'banner', status: 'error', content: content -%}
              {%- endif -%}
            {%- endif -%}

            <div class="fieldset">
              <div class="fieldset-row">
                {%- assign label = 'blog.comments.name' | t -%}
                {%- render 'input', type: 'text', name: 'comment[author]', label: label, value: customer.name, required: true, autocomplete: 'name' -%}

                {%- assign label = 'blog.comments.email' | t -%}
                {%- render 'input', type: 'email', name: 'comment[email]', label: label, value: customer.email, required: true, autocomplete: 'email' -%}
              </div>

              {%- assign label = 'blog.comments.message' | t -%}
              {%- render 'input', name: 'comment[body]', label: label, multiline: 4, required: true -%}
            </div>

            {%- if blog.moderated? -%}
              <p>{{ 'blog.comments.moderated' | t }}</p>
            {%- endif -%}

            <div class="align-self-start">
              {%- assign button_content = 'blog.comments.submit' | t -%}
              {%- render 'button', type: 'submit', content: button_content -%}
            </div>
          {%- endform -%}
        </div>
      </div>
    {%- endif -%}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.main_article.name",
  "class": "shopify-section--main-article",
  "tag": "section",
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:global.colors.scheme",
      "default": "scheme-1"
    },
    {
      "type": "paragraph",
      "content": "t:sections.main_article.comments_category_info"
    },
    {
      "type": "header",
      "content": "t:global.image.image"
    },
    {
      "type": "checkbox",
      "id": "show_image",
      "label": "t:sections.main_article.show_image",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_parallax",
      "label": "t:sections.main_article.enable_parallax",
      "default": true
    },
    {
      "type": "select",
      "id": "image_size",
      "label": "t:sections.main_article.image_height",
      "options": [
        {
          "label": "t:global.sizes.original_image_ratio",
          "value": "auto"
        },
        {
          "label": "t:global.sizes.small",
          "value": "small"
        },
        {
          "label": "t:global.sizes.medium",
          "value": "medium"
        },
        {
          "label": "t:global.sizes.large",
          "value": "large"
        }
      ],
      "default": "medium"
    },
    {
      "type": "header",
      "content": "t:global.text.content"
    },
    {
      "type": "select",
      "id": "content_width",
      "label": "t:global.sizes.content_width",
      "options": [
        {
          "label": "t:global.sizes.x_small",
          "value": "xs"
        },
        {
          "label": "t:global.sizes.small",
          "value": "sm"
        },
        {
          "label": "t:global.sizes.medium",
          "value": "md"
        },
        {
          "label": "t:global.sizes.large",
          "value": "lg"
        }
      ],
      "default": "xs"
    },
    {
      "type": "checkbox",
      "id": "show_date",
      "label": "t:global.blog.show_date",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_category",
      "label": "t:global.blog.show_category",
      "info": "t:global.blog.show_category_info",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_author",
      "label": "t:global.blog.show_author",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_share_buttons",
      "label": "t:sections.main_article.show_share_buttons",
      "default": true
    },
    {
      "type": "header",
      "content": "t:sections.main_article.toolbar_category"
    },
    {
      "type": "checkbox",
      "id": "show_sticky_bar",
      "label": "t:sections.main_article.show_sticky_bar",
      "default": true
    },
    {
      "type": "color_scheme",
      "id": "toolbar_color_scheme",
      "label": "t:global.colors.scheme",
      "default": "scheme-3"
    }
  ]
}
{% endschema %}
