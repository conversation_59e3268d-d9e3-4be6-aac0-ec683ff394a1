{%- if block.settings.text != blank -%}
  {%- assign alignment = block.settings.alignment | replace: 'left', 'start' | replace: 'right', 'end' -%}

  <div class="justify-items-{{ alignment }}" {{ block.shopify_attributes }}>
    <span class="badge" style="background-color: {{ block.settings.background }}; color: {{ block.settings.text_color }};">
      {{- block.settings.text -}}
    </span>
  </div>
{%- endif -%}
  
{% schema %}
{
  "name": "t:blocks.badge.name",
  "tag": null,
  "settings": [
    {
      "type": "text",
      "id": "text",
      "label": "t:global.text.text",
      "default": "Text"
    },
    {
      "type": "text_alignment",
      "id": "alignment",
      "label": "t:blocks.badge.alignment",
      "default": "center"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "t:blocks.badge.text_color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "background",
      "label": "t:blocks.badge.background",
      "default": "#000000"
    }
  ],
  "presets": [
    {
      "name": "t:blocks.badge.name"
    }
  ]
}
{% endschema %}