{%- if block.settings.text != blank -%}
  {%- render 'button', 
    href: block.settings.url,
    content: block.settings.text,
    background: block.settings.background, 
    text_color: block.settings.text_color,
    block: block -%}
{%- endif -%}

{% schema %}
{
  "name": "t:blocks.button.name",
  "tag": null,
  "settings": [
  {
    "type": "text",
    "id": "text",
    "label": "t:global.text.text",
    "default": "Button"
  },
  {
    "type": "url",
    "id": "url",
    "label": "t:global.text.link"
  },
  {
    "type": "color",
    "id": "background",
    "label": "t:global.colors.background"
  },
  {
    "type": "color",
    "id": "text_color",
    "label": "t:global.text.text"
  }
  ],
  "presets": [
    {
      "name": "t:blocks.button.name"
    }
  ]
}
{% endschema %}