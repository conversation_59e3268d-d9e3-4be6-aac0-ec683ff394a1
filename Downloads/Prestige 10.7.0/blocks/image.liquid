{%- if block.settings.image != blank -%}
  {%- assign alignment = block.settings.alignment | replace: 'left', 'start' | replace: 'right', 'end' -%}

  <div class="grid" {{ block.shopify_attributes }}>
    {%- if block.settings.width_mode == 'custom' -%}
      {%- capture image_style_attribute -%}--image-max-width: {{ block.settings.max_width }}px; --image-mobile-max-width: {{ block.settings.mobile_max_width }}px{%- endcapture -%}
      {%- capture image_sizes_attribute -%}(max-width: 699px) {{ block.settings.mobile_max_width }}px, {{ block.settings.max_width }}px{%- endcapture -%}
    {%- else -%}
      {%- capture image_sizes_attribute -%}100vw{%- endcapture -%}
    {%- endif -%}

    <div class="justify-self-{{ alignment }}">
      <picture>
        {%- if block.settings.mobile_image != blank -%}
          <source
            media="(max-width: 699px)"
            srcset="{{ block.settings.mobile_image | image_url: width: 400 }} 400w, {{ block.settings.mobile_image | image_url: width: 600 }} 600w, {{ block.settings.mobile_image | image_url: width: 800 }} 800w, {{ block.settings.mobile_image | image_url: width: 1000 }} 1000w, {{ block.settings.mobile_image | image_url: width: 1200 }} 1200w"
          >
        {%- endif -%}

        {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: sizes: image_sizes_attribute, widths: '200,300,400,500,600,800,1000,1200,1400', class: 'constrained-image', style: image_style_attribute -}}
      </picture>
    </div>
  </div>
{%- endif -%}

{% schema %}
{
  "name": "t:blocks.image.name",
  "tag": null,
  "settings": [
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:global.image.image",
      "info": "t:blocks.image.desktop_image_size_recommendation"
    },
    {
      "type": "image_picker",
      "id": "mobile_image",
      "label": "t:global.image.mobile_image",
      "info": "t:blocks.image.mobile_image_size_recommendation"
    },
    {
      "type": "text_alignment",
      "id": "alignment",
      "label": "t:global.image.alignment",
      "default": "center"
    },
    {
      "type": "radio",
      "id": "width_mode",
      "label": "t:global.image.width",
      "options": [
        {
          "value": "full_width",
          "label": "t:global.image.width_options.full_width"
        },
        {
          "value": "custom",
          "label": "t:global.image.width_options.custom"
        }
      ],
      "default": "custom"
    },
    {
      "type": "range",
      "id": "max_width",
      "min": 100,
      "max": 1000,
      "step": 10,
      "unit": "px",
      "label": "t:global.image.maximum_width",
      "default": 300
    },
    {
      "type": "range",
      "id": "mobile_max_width",
      "min": 100,
      "max": 600,
      "step": 10,
      "unit": "px",
      "label": "t:global.image.mobile_maximum_width",
      "default": 200
    }
  ],
  "presets": [
    {
      "name": "t:blocks.image.name"
    }
  ]
}
{% endschema %}