{%- comment -%}
----------------------------------------------------------------------------------------------------------------------
PRICE RANGE
----------------------------------------------------------------------------------------------------------------------

Create a price range selector (used in product faceting)

********************************************
Supported variables
********************************************

* filter: the filter object to use
{%- endcomment -%}

<price-range class="price-range">
  {%- liquid
    assign min_value = filter.min_value.value | default: 0.0 | divided_by: 100.0
    assign max_value = filter.max_value.value | default: filter.range_max | divided_by: 100.0
    assign range_max = filter.range_max | divided_by: 100.0 | ceil
    assign lower_bound_progress = min_value | divided_by: range_max | times: 100.0
    assign higher_bound_progress = max_value | divided_by: range_max | times: 100.0
  -%}

  <div class="range-group" style="--range-min: {{ lower_bound_progress }}%; --range-max: {{ higher_bound_progress }}%">
    <input type="range" aria-label="{{ 'collection.faceting.price_filter_from' | t }}" class="range" min="0" max="{{ range_max | ceil }}" value="{{ min_value | ceil }}">
    <input type="range" aria-label="{{ 'collection.faceting.price_filter_to' | t }}" class="range" min="0" max="{{ range_max | ceil }}" value="{{ max_value | ceil }}">
  </div>

  <div class="input-group">
    <label class="input-prefix text-sm">
      <span class="prefix text-subdued">{{ cart.currency.symbol }}{% if settings.currency_code_enabled %}{{ cart.currency.iso_code }}{% endif %}</span>
      <input aria-label="{{ 'collection.faceting.price_filter_from' | t }}" class="field" type="number" inputmode="numeric" {% if filter.min_value.value %}value="{{ min_value | ceil }}"{% endif %} name="{{ filter.min_value.param_name }}" id="{{ filter.min_value.param_name }}" min="0" max="{{ max_value | ceil }}" placeholder="0" autocomplete="off">
    </label>

    <span class="text-subdued text-sm">{{ 'collection.faceting.price_range_to' | t }}</span>

    <label class="input-prefix text-sm">
      <span class="prefix text-subdued">{{ cart.currency.symbol }}{% if settings.currency_code_enabled %}{{ cart.currency.iso_code }}{% endif %}</span>
      <input aria-label="{{ 'collection.faceting.price_filter_to' | t }}" class="field" type="number" inputmode="numeric" {% if filter.max_value.value %}value="{{ max_value | ceil }}"{% endif %} name="{{ filter.max_value.param_name }}" id="{{ filter.max_value.param_name }}" min="{{ min_value | ceil }}" max="{{ range_max | ceil }}" placeholder="{{ range_max | ceil }}" autocomplete="off">
    </label>
  </div>
</price-range>