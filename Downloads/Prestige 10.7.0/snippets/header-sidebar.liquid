{%- comment -%}
----------------------------------------------------------------------------------------------------------------------
HEADER SIDEBAR COMPONENT
----------------------------------------------------------------------------------------------------------------------

This snippet the navigation sidebar for the header. It is used for both the mobile menu and for the desktop menu when
the merchant configures the drawer mode.

********************************************
Supported variables
********************************************

* menu: the menu to render
* color_scheme: the color scheme to use for the sidebar
* show_dividers: whether dividers are shown for the collapsible
* open_second_level_menus: if true, second level dropdown are open by default
{%- endcomment -%}

{%- comment -%}
IMPLEMENTATION NOTE: while being a drawer, the sidebar has a completely different structure for the content. We are
therefore using a different structure for the Shadow DOM template
{%- endcomment -%}
<template id="header-sidebar-template">
  <div part="base">
    <div part="overlay"></div>

    <div part="content">
      <header part="header">
        <dialog-close-button class="contents">
          <button type="button" part="close-button tap-area" aria-label="{{ 'general.accessibility.close' | t | escape }}">
            {%- render 'icon' with 'close', width: 16 -%}
          </button>
        </dialog-close-button>
      </header>

      <div part="panel-list">
        <slot name="main-panel"></slot>

        {%- if menu.levels > 0 -%}
          <slot name="collapsible-panel"></slot>
        {%- endif -%}
      </div>
    </div>
  </div>
</template>

<header-sidebar id="sidebar-menu" class="header-sidebar drawer drawer--sm color-scheme color-scheme--{{ color_scheme.id }}" template="header-sidebar-template" open-from="left">
  {%- comment -%}We are using a Shadow DOM where each panel has a slot, so we can directly render each level individually{%- endcomment -%}

  {%- comment -%}Panel 1 is the first level{%- endcomment -%}
  <div class="header-sidebar__main-panel" slot="main-panel">
    <div class="header-sidebar__scroller">
      <ul class="header-sidebar__linklist {% if show_dividers %}divide-y{% endif %} unstyled-list" role="list">
        {%- for link in menu.links -%}
          <li>
            {%- if link.links.size > 0 -%}
              <button type="button" class="header-sidebar__linklist-button {% if section.settings.sidebar_text_font == 'heading' %}h6{% endif %}" aria-controls="header-panel-{{ forloop.index }}" aria-expanded="false">
                {{- link.title -}}
                {%- render 'icon' with 'chevron-right', width: 12, direction_aware: true -%}
              </button>
            {%- else -%}
              <a href="{{ link.url }}" class="header-sidebar__linklist-button {% if section.settings.sidebar_text_font == 'heading' %}h6{% endif %}">
                {{- link.title -}}
              </a>
            {%- endif -%}
          </li>
        {%- endfor -%}
      </ul>
    </div>

    {%- liquid
      if section.settings.show_country_selector and localization.available_countries.size > 1
        assign show_country_selector = true
      endif

      if section.settings.show_locale_selector and localization.available_languages.size > 1
        assign show_locale_selector = true
      endif
    -%}

    {%- if shop.customer_accounts_enabled or show_country_selector or show_locale_selector -%}
      <div class="header-sidebar__footer">
        {%- if shop.customer_accounts_enabled -%}
          <a href="{% if customer %}{{ routes.account_url }}{% else %}{{ routes.account_login_url }}{% endif %}" class="text-with-icon smallcaps sm:hidden">
            {%- render 'icon' with 'account', width: 20 -%}
            
            {%- if customer -%}
              {{- 'header.general.account' | t -}}
            {%- else -%}
              {{- 'header.general.login' | t -}}
            {%- endif -%}
          </a>
        {%- endif -%}

        {%- if show_country_selector or show_locale_selector -%}
          <div class="localization-selectors">
            {%- if show_country_selector -%}
              {%- render 'localization-selector', type: 'country', placement: 'top-start', show_country_name: section.settings.show_country_name, show_country_flag: section.settings.show_country_flag, id_prefix: 'header-sidebar' -%}
            {%- endif -%}

            {%- if show_country_selector and show_locale_selector -%}
              <span class="localization-selectors__separator" aria-hidden="true"></span>
            {%- endif -%}

            {%- if show_locale_selector -%}
              {%- render 'localization-selector', type: 'locale', placement: 'top-start', id_prefix: 'header-sidebar' -%}
            {%- endif -%}
          </div>
        {%- endif -%}
      </div>
    {%- endif -%}
  </div>

  {%- comment -%}Panel 2 holds all the second levels{%- endcomment -%}
  {%- if menu.levels > 0 -%}
    <header-sidebar-collapsible-panel class="header-sidebar__collapsible-panel" slot="collapsible-panel">
      <div class="header-sidebar__scroller">
        {%- for link in menu.links -%}
          {%- if link.links.size > 0 -%}
            <div id="header-panel-{{ forloop.index }}" class="header-sidebar__sub-panel" hidden>
              <button type="button" class="header-sidebar__back-button link-faded {% if show_dividers %}is-divided{% endif %} text-with-icon {% if section.settings.sidebar_text_font == 'heading' %}h6{% endif %} md:hidden" data-action="close-panel">
                {%- render 'icon' with 'chevron-left', width: 12, direction_aware: true -%}
                {{- link.title -}}
              </button>

              <ul class="header-sidebar__linklist {% if show_dividers %}divide-y{% endif %} unstyled-list" role="list">
                {%- for sub_link in link.links -%}
                  <li>
                    {%- if sub_link.links.size > 0 -%}
                      <accordion-disclosure>
                        <details class="accordion__disclosure group" {% if open_second_level_menus %}open{% endif %}>
                          <summary class="header-sidebar__linklist-button {% if section.settings.sidebar_text_font == 'heading' %}h6{% endif %}">
                            {{- sub_link.title -}}
                            <span class="animated-plus group-expanded:rotate" aria-hidden="true"></span>
                          </summary>

                          <div class="header-sidebar__nested-linklist">
                            {%- for sub_sub_link in sub_link.links -%}
                              <a href="{{ sub_sub_link.url }}" class="link-faded-reverse">{{ sub_sub_link.title }}</a>
                            {%- endfor -%}
                          </div>
                        </details>
                      </accordion-disclosure>
                    {%- else -%}
                      <a href="{{ sub_link.url }}" class="header-sidebar__linklist-button {% if section.settings.sidebar_text_font == 'heading' %}h6{% endif %}">
                        {{- sub_link.title -}}
                      </a>
                    {%- endif -%}
                  </li>
                {%- endfor -%}
              </ul>

              {%- liquid
                assign link_title_downcase = link.title | strip | downcase
                assign mega_menu_block = ''

                for block in section.blocks
                  assign menu_item_downcase = block.settings.menu_item | strip | downcase

                  if menu_item_downcase == link_title_downcase
                    assign mega_menu_block = block
                    break
                  endif

                endfor
              -%}

              {%- capture mega_menu_content -%}
                {%- render 'mega-menu-images', context: 'sidebar', block: mega_menu_block -%}
              {%- endcapture -%}

              {%- if mega_menu_content != blank -%}
                <div class="header-sidebar__promo scroll-area bleed md:unbleed">
                  {{- mega_menu_content -}}
                </div>
              {%- endif -%}
            </div>
          {%- endif -%}
        {%- endfor -%}
      </div>
    </header-sidebar-collapsible-panel>
  {%- endif -%}
</header-sidebar>