{%- comment -%}
----------------------------------------------------------------------------------------------------------------------
MEDIA COMPONENT
----------------------------------------------------------------------------------------------------------------------

Renders a single media. This snippet can be used for product media, but also for media coming from metafields.

********************************************
Supported variables
********************************************

* media: the media to render (required)
* sizes: an optional sizes attribute used for the image generation
* preload: if set to true, the image is preloaded and its fetch priority is higher
* autoplay: a boolean indicating if media of type video should autoplay. If set to false, a "play button" will be displayed on top of video
* loop: a boolean indicating if media type of type video should loop
* playsinline: controls if the video should be played inline. If none is passed, it will be set to true if the video is autoplaying
* muted: controls if the video sould be muted. If none is passed, it will be set to true if the video is autoplaying
* controls: if set to true, then the controls for the video are displayed
* group: an optional group to set for the media. When a group is set, only one media at a time of the given group can play.
* show_play_button: by default, a play button is added when video is not autoplaying. By setting it to false you can force turning off this behavior.
* play_button_background: an optional background color for the play button
* product: the optional product for 3D models
* class: optional class applied on the item
{%- endcomment -%}

{%- liquid
  if preload
    assign loading = 'eager'
    assign fetchpriority = 'high'
  else
    assign loading = 'lazy'
    assign fetchpriority = 'auto'
  endif
-%}

{%- case media.media_type -%}
  {%- when 'image' -%}
    {{- media | image_url: width: media.preview_image.width | image_tag: loading: loading, fetchpriority: fetchpriority, sizes: sizes, widths: '200,300,400,500,600,700,800,1000,1200,1400,1600,1800,2000,2200,2400,2600,2800,3000,3200', class: class -}}

  {%- when 'video', 'external_video' -%}
    <video-media {% if class %}class="{{ class }}"{% endif %} type="{{ media.media_type }}" {% if media.host %}host="{{ media.host }}"{% endif %} {% if autoplay %}autoplay{% endif %} {% if autoplay != true and show_play_button != false %}show-play-button{% endif %} {% if group != blank %}group="{{ group | escape }}"{% endif %} style="--aspect-ratio: {{ media.aspect_ratio }}; --play-button-background: {{ play_button_background | default: settings.default_color_scheme.settings.background }}">
      {%- unless autoplay -%}
        {{- media | image_url: width: media.preview_image.width | image_tag: loading: loading, fetchpriority: fetchpriority, sizes: sizes, widths: '200,300,400,500,600,700,800,1000,1200,1400,1600,1800,2000,2200,2400,2600,2800,3000,3200' -}}
      {%- endunless -%}

      {%- if media.media_type == 'video' -%}
        {%- liquid
          if preload
            assign preload_attribute = 'metadata'
          else
            assign preload_attribute = 'none'
          endif

          assign playsinline = playsinline | default: autoplay, allow_false: true
          assign muted = muted | default: autoplay, allow_false: true

          echo media | video_tag: controls: controls, playsinline: playsinline, muted: muted, loop: loop, preload: preload_attribute, image_size: '400x'
        -%}
      {%- else -%}
        <template>
          {%- liquid
            if media.host == 'youtube'
              echo media | external_video_url: enablejsapi: true, loop: loop, mute: autoplay, autoplay: true | external_video_tag
            elsif media.host == 'vimeo'
              echo media | external_video_url: autopause: true, loop: loop, background: autoplay, muted: autoplay, autoplay: true | external_video_tag
            else
              echo media | external_video_tag: image_size: '2048x'
            endif
          -%}
        </template>
      {%- endif -%}
    </video-media>

  {%- when 'model' -%}
    <model-media {% if class %}class="{{ class }}"{% endif %} {% if group != blank %}group="{{ group | escape }}"{% endif %} handle="{{ product.handle }}" style="aspect-ratio: {{ media.preview_image.aspect_ratio }}">
      {{- media | model_viewer_tag: image_size: '2048x', reveal: 'interaction', toggleable: true -}}
    </model-media>
{%- endcase -%}