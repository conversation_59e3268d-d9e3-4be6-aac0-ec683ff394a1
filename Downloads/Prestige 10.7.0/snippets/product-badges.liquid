{%- comment -%}
----------------------------------------------------------------------------------------------------------------------
PRODUCT BADGES COMPONENT
----------------------------------------------------------------------------------------------------------------------

This component is used in product listing and product page to render the badges of a given product

********************************************
Supported variables
********************************************

* product: the product to render the badges
* variant: the specific variant to show the badge from
* vertical: if set to true, the badges are outputted vertically
* types: the types of badge to output. Can be "custom", "sold_out" or "discount" (or a combination separated by comma). If nothing is set, all badges are outputted.
* context: can be "product" or "card"
{%- endcomment -%}

{%- liquid
  assign badge_types = types | default: 'custom, sold_out, discount' | split: ','
  assign variant = variant | default: product.selected_or_first_available_variant
-%}

{%- capture badges -%}
  {%- for badge_type in badge_types -%}
    {%- assign stripped_badge_type = badge_type | strip -%}

    {%- case stripped_badge_type -%}
      {%- when 'custom' -%}
        {%- assign custom_badges = product.metafields.custom.badges.value -%}

        {%- for custom_badge in custom_badges -%}
          <span class="badge badge--custom badge--{{ custom_badge | handle }}">{{ custom_badge }}</span>
        {%- endfor -%}

      {%- when 'sold_out' -%}
        {%- if settings.show_sold_out_badge and variant.available == false -%}
          <sold-out-badge class="badge badge--sold-out">
            {{- 'product.general.sold_out_badge' | t -}}
          </sold-out-badge>
        {%- endif -%}

      {%- when 'discount' -%}
        {%- if settings.show_discount -%}
          {%- liquid
            assign show_on_sale_badge = false

            if context == 'product'
              if variant.compare_at_price > variant.price
                assign show_on_sale_badge = true
              endif
            elsif context == 'card'
              if product.compare_at_price > product.price
                assign show_on_sale_badge = true
              endif
            endif
          -%}

          {%- if product.compare_at_price > product.price -%}
            {%- if settings.discount_mode == 'percentage' -%}
              {%- assign savings = variant.compare_at_price | minus: variant.price | times: 100.0 | divided_by: variant.compare_at_price | round | append: '%' -%}
            {%- else -%}
              {%- capture savings -%}{{ variant.compare_at_price | minus: variant.price | money }}{%- endcapture -%}
            {%- endif -%}

            {%- if show_on_sale_badge -%}
              <on-sale-badge class="badge badge--on-sale">
                {%- liquid
                  if context == 'product'
                    echo 'product.general.discount_badge_html' | t: savings: savings
                  elsif context == 'card'
                    if product.compare_at_price_varies or variant.compare_at_price == blank
                      echo 'product.general.on_sale_badge' | t
                    else
                      echo 'product.general.discount_badge_html' | t: savings: savings
                    endif
                  endif
                -%}
              </on-sale-badge>
            {%- endif -%}
          {%- endif -%}
        {%- endif -%}
    {%- endcase -%}
  {%- endfor -%}
{%- endcapture -%}

{%- if badges != blank -%}
  <badge-list class="badge-list {% if vertical %}badge-list--vertical{% endif %}">
    {{- badges -}}
  </badge-list>
{%- endif -%}