{%- comment -%}
----------------------------------------------------------------------------------------------------------------------
COMPLEMENTARY PRODUCTS
----------------------------------------------------------------------------------------------------------------------

This snippet renders the complementary products block used on product page. Because those complementary products can
be added both below the gallery or on the right, this is extracted into its own snippet to reduce code duplication

********************************************
Supported variables
********************************************

* product: the product to which we render the complementary products
* block: the block containing the information to render itself
{%- endcomment -%}

<product-recommendations class="block" hidden product="{{ product.id }}" limit="{{ block.settings.products_count }}" intent="complementary">
  {%- if recommendations.performed and recommendations.intent == 'complementary' and recommendations.products_count > 0 -%}
    <div class="complementary-products">
      {%- assign carousel_id = 'complementary-products-carousel-' | append: block.id -%}

      {%- if block.settings.title != blank or block.settings.stack_products == false and recommendations.products_count > 1 -%}
        <div class="complementary-products__header {% if block.settings.show_below_gallery and block.settings.stack_products %}justify-center{% endif %}">
          <p class="h5">{{ block.settings.title }}</p>

          {%- if block.settings.stack_products == false and recommendations.products_count > 1 -%}
            <carousel-navigation aria-controls="{{ carousel_id }}" class="page-dots page-dots--narrow sm-max:hidden">
              {%- for i in (1..recommendations.products_count) -%}
                <button class="relative" aria-current="{% if forloop.first %}true{% else %}false{% endif %}">
                  <span class="sr-only">{{ 'general.accessibility.go_to_item' | t: index: forloop.index }}</span>
                </button>
              {%- endfor -%}
            </carousel-navigation>
          {%- endif -%}
        </div>
      {%- endif -%}

      {%- capture complementary_products -%}
        {%- for product in recommendations.products -%}
          {%- render 'product-card-horizontal', product: product, show_quick_buy: block.settings.show_quick_buy -%}
        {%- endfor -%}
      {%- endcapture -%}

      {%- if block.settings.stack_products -%}
        <div class="complementary-products__product-list">
          {{- complementary_products -}}
        </div>
      {%- else -%}
        <div class="sm-max:hidden">
          <scroll-carousel id="{{ carousel_id }}" class="complementary-products__product-list complementary-products__product-list--carousel scroll-area snap-x">
            {{- complementary_products -}}
          </scroll-carousel>
        </div>

        <div class="v-stack gap-5 sm:hidden">
          {%- assign carousel_mobile_id = 'complementary-products-mobile-carousel-' | append: block.id -%}

          <scroll-carousel id="{{ carousel_mobile_id }}" class="complementary-products__product-list complementary-products__product-list--carousel bleed scroll-area snap-x">
            {%- for product in recommendations.products -%}
              {%- render 'product-card', product: product, show_quick_buy: block.settings.show_quick_buy, show_badges: false, show_vendor: false, show_rating: false, show_secondary_image: false, show_swatches: false -%}
            {%- endfor -%}
          </scroll-carousel>

          {%- if recommendations.products_count > 1 -%}
            <carousel-navigation aria-controls="{{ carousel_mobile_id }}" class="page-dots">
              {%- for i in (1..recommendations.products_count) -%}
                <button class="relative" aria-current="{% if forloop.first %}true{% else %}false{% endif %}">
                  <span class="sr-only">{{ 'general.accessibility.go_to_item' | t: index: forloop.index }}</span>
                </button>
              {%- endfor -%}
            </carousel-navigation>
          {%- endif -%}
        </div>
      {%- endif -%}
    </div>
  {%- endif -%}
</product-recommendations>