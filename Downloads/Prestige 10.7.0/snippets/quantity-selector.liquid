{%- comment -%}
----------------------------------------------------------------------------------------------------------------------
QUANTITY SELECTOR
----------------------------------------------------------------------------------------------------------------------

This component renders a quantity selector that can be used either on product page (by passing a "variant") or on a
cart page (by passing a "line_item"). It also automatically checks for inventory in the case of cart page.

********************************************
Supported variables
********************************************

* variant: when the quantity selector is used in product page context
* line_item: when the quantity selector is used in cart page context
* quick_order: when set to true, the variant will be used in context of quick order. Specifically, this will
               use a quantity of zero if nothing is in the cart yet.
* size: if set to "sm", we render a reduced size
* form: an optional form ID to submit to
* show_spinner: when set to true, a spinner is added to the quantity selector, that is visible when the "aria-busy" 
                attribute is set to true
{%- endcomment -%}

<quantity-selector class="quantity-selector {% if size %}quantity-selector--{{ size }}{% endif %}" {% if quick_order %}allow-reset-to-zero{% endif %}>
  {%- liquid
    assign max_quantity = ''
    assign variant = line_item.variant | default: variant

    # When we don't have line_item (example: product page), we don't limit the quantity the buyer can choose
    if variant.inventory_management != blank and variant.inventory_policy == 'deny'
      if line_item != blank
        assign current_quantity_for_variant = cart | item_count_for_variant: variant.id
        assign max_quantity = variant.inventory_quantity | minus: current_quantity_for_variant | plus: line_item.quantity
      elsif quick_order
        assign max_quantity = variant.inventory_quantity
      endif
    endif

    if variant.quantity_rule.max != nil
      assign max_quantity = max_quantity | default: 999999 | at_most: variant.quantity_rule.max
    endif

    if line_item != blank
      assign quantity_value = line_item.quantity
    else
      if quick_order
        assign quantity_value = cart | item_count_for_variant: variant.id
      else
        assign quantity_value = variant.quantity_rule.min | default: 1
      endif
    endif
  -%}

  {%- if line_item == blank -%}
    {%- comment -%}For quick order, we want to always allow to press minus to go back to zero{%- endcomment -%}
    <button type="button" class="quantity-selector__button" {% if quantity_value == 0 or quick_order != true and quantity_value <= variant.quantity_rule.min %}disabled{% endif %}>
      <span class="sr-only">{{ 'product.quantity.decrease_quantity' | t }}</span>
      {%- render 'icon' with 'minus', width: 10 -%}
    </button>
  {%- else -%}
    <a href="{{ routes.cart_change_url }}?id={{ line_item.key }}&quantity={{ line_item.quantity | minus: line_item.variant.quantity_rule.increment }}" class="quantity-selector__button">
      <span class="sr-only">{{ 'product.quantity.decrease_quantity' | t }}</span>
      {%- render 'icon' with 'minus', width: 10 -%}
    </a>
  {%- endif -%}

  <quantity-input class="quantity-selector__input-wrapper">
    <input class="quantity-selector__input {% if size == 'sm' %}text-sm{% endif %}" type="number" value="{{ quantity_value }}" name="{% if line_item != blank %}updates[]{% else %}quantity{% endif %}" inputmode="numeric" step="{{ variant.quantity_rule.increment }}" min="{{ variant.quantity_rule.min }}" {% if max_quantity != blank %}max="{{ max_quantity }}"{% endif %} {% if form %}form="{{ form }}"{% endif %} {% if line_item != blank %}data-line-key="{{ line_item.key }}"{% endif %} aria-label="{{ 'product.quantity.change_quantity' | t | escape }}">

    {%- if show_spinner -%}
      <svg class="quantity-selector__spinner" width="14" height="14" fill="none" viewBox="0 0 14 14">
        <circle cx="7" cy="7" r="6" stroke="currentColor" stroke-width="1.5" opacity=".2" />
        <path stroke="currentColor" stroke-width="1.5" d="M11.638 3.194a6 6 0 1 0-.395 8.049" />
      </svg>
    {%- endif -%}
  </quantity-input>

  {%- if line_item == blank or max_quantity != blank and line_item.quantity >= max_quantity -%}
    <button type="button" class="quantity-selector__button" {% if line_item != blank %}disabled data-tooltip="{{ 'product.quantity.cannot_add_more' | t | escape }}"{% endif %}>
      <span class="sr-only">{{ 'product.quantity.increase_quantity' | t }}</span>
      {%- render 'icon' with 'plus', width: 10 -%}
    </button>
  {%- else -%}
    <a href="{{ routes.cart_change_url }}?id={{ line_item.key }}&quantity={{ line_item.quantity | plus: line_item.variant.quantity_rule.increment }}" class="quantity-selector__button">
      <span class="sr-only">{{ 'product.quantity.increase_quantity' | t }}</span>
      {%- render 'icon' with 'plus', width: 10 -%}
    </a>
  {%- endif -%}
</quantity-selector>