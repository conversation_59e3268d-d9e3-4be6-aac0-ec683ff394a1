{%- comment -%}
----------------------------------------------------------------------------------------------------------------------
PRODUCT CARD HORIZONTAL COMPONENT
----------------------------------------------------------------------------------------------------------------------

This component is used in complementary product to show a condensed view of the product

********************************************
Supported variables
********************************************

* product: the product to render
* show_quick_buy: show or not the quick buy (which open a modal if the product contains more than 1 choice)
{%- endcomment -%}

<div class="horizontal-product-card">
  {%- if product.featured_media -%}
    <a href="{{ product.url }}" class="horizontal-product-card__figure" data-instant>
      {{- product.featured_media | image_url: width: product.featured_media.width | image_tag: loading: 'lazy', sizes: '100px', widths: '100,150,200,250,300', class: 'horizontal-product-card__image' -}}
    </a>
  {%- endif -%}

  <div class="horizontal-product-card__info">
    <div class="v-stack gap-1 justify-items-start">
      <a href="{{ product.url }}" class="product-title {% if settings.product_card_text_font == 'heading' %}h6{% endif %}" data-instant>{{ product.title }}</a>
      {%- render 'price-list', product: product, context: 'card' -%}
    </div>

    {%- if product.available and show_quick_buy -%}
      {%- if product.variants.size == 1 -%}
        {%- comment -%}With one variant, we can simply render a form for direct add{%- endcomment -%}
        <product-form>
          {%- form 'product', product -%}
            <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
            <input type="hidden" name="quantity" value="1">
            <input type="hidden" name="on_success" value="force_open_drawer">

            <button type="submit" class="@narrow:horizontal-product-card__button link">{{ 'product.general.add_to_cart_button' | t }}</button>
            <button type="submit" class="@large:horizontal-product-card__button button button--outline button--subdued">{{ 'product.general.add_to_cart_button' | t }}</button>
          {%- endform -%}
        </product-form>
      {%- else -%}
        {%- comment -%}Otherwise, we need to open a modal{%- endcomment -%}
        {%- assign modal_id = 'modal-' | append: block.id | append: '-' | append: product.id -%}

        <quick-buy-modal id="{{ modal_id }}" handle="{{ product.handle }}?variant={{ product.selected_or_first_available_variant.id }}" class="quick-buy-modal modal modal--lg color-scheme color-scheme--dialog">
          {%- comment -%}Content is filled dynamically in Ajax for performance reasons{%- endcomment -%}
          LOADING
        </quick-buy-modal>

        <button aria-controls="{{ modal_id }}" class="@narrow:horizontal-product-card__button link">{{ 'product.general.add_to_cart_button' | t }}</button>
        <button aria-controls="{{ modal_id }}" class="@large:horizontal-product-card__button button button--outline button--subdued">{{ 'product.general.add_to_cart_button' | t }}</button>
      {%- endif -%}
    {%- endif -%}
  </div>
</div>
