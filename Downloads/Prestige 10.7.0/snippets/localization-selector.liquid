{%- comment -%}
----------------------------------------------------------------------------------------------------------------------
LOCALIZATION SELECTOR COMPONENT
----------------------------------------------------------------------------------------------------------------------

This component renders a selector for a localization component (either country or locale).

********************************************
Supported variables
********************************************

* type: can be either "country" or "locale" (required)
* show_country_name: for type "country", if set to true the country name is shown
* show_country_flag: for type "country", if set to true the country flag is shown
* id_prefix: an option to help dissociate the ID in case this is included several times in the same section
{%- endcomment -%}

{%- capture localization_form_id -%}localization-form-{{ id_prefix }}-{{ section.id }}-{{ type }}{%- endcapture -%}
{%- capture localization_popover_id -%}popover-localization-{{ id_prefix }}-{{ section.id }}-{{ type }}{%- endcapture -%}

{%- case type -%}
  {%- when 'country' -%}
    {%- comment -%}
    ----------------------------------------------------------------------------------------------
    COUNTRY SELECTOR
    ----------------------------------------------------------------------------------------------
    {%- endcomment -%}

    <div class="relative">
      <button type="button" class="localization-toggle heading text-xxs link-faded" aria-controls="{{ localization_popover_id }}" aria-label="{{ 'general.localization.change_country_accessibility_text' | t | escape }}" aria-expanded="false">
        {%- if show_country_flag -%}
          {{- localization.country | image_url: width: 60, format: 'jpg' | image_tag: class: 'country-flag' -}}
        {%- endif -%}

        {%- if show_country_name -%}
          <span>{{ localization.country.name }} ({{ localization.country.currency.iso_code }} {% if localization.country.currency.symbol %}{{ localization.country.currency.symbol }}){%- endif -%}</span>
        {%- else -%}
          <span>{{ localization.country.currency.iso_code }} {% if localization.country.currency.symbol %}{{ localization.country.currency.symbol }}{%- endif -%}</span>
        {%- endif -%}

        {%- render 'icon' with 'chevron-down' -%}
      </button>

      <x-popover id="{{ localization_popover_id }}" initial-focus="[aria-selected='true']" class="popover popover--{{ placement | default: 'bottom-start' }} color-scheme color-scheme--dialog">
        <p class="h4" slot="header">{{ 'general.localization.country' | t }}</p>

        {%- form 'localization', id: localization_form_id -%}
          <x-listbox class="popover__value-list">
            {%- for country in localization.available_countries -%}
              {%- assign is_selected = false -%}

              {%- if country.iso_code == localization.country.iso_code -%}
                {%- assign is_selected = true -%}
              {%- endif -%}

              <button type="submit" name="country_code" class="popover__value-option h-stack gap-2.5" role="option" value="{{ country.iso_code }}" aria-selected="{% if is_selected %}true{% else %}false{% endif %}">
                {%- if show_country_flag -%}
                  {{- country | image_url: width: 60, format: 'jpg' | image_tag: loading: 'lazy', class: 'country-flag' -}}
                {%- endif -%}

                <span>{{- country.name }} ({{ country.currency.iso_code }} {% if country.currency.symbol %}{{ country.currency.symbol }}{%- endif -%})</span>
              </button>
            {%- endfor -%}
          </x-listbox>
        {%- endform -%}
      </x-popover>
    </div>

  {%- when 'locale' -%}
    {%- comment -%}
    ----------------------------------------------------------------------------------------------
    LOCALE SELECTOR
    ----------------------------------------------------------------------------------------------
    {%- endcomment -%}

    <div class="relative">
      <button type="button" class="localization-toggle heading text-xxs link-faded" aria-controls="{{ localization_popover_id }}" aria-label="{{ 'general.localization.change_language_accessibility_text' | t | escape }}" aria-expanded="false">
        {{- localization.language.endonym_name | capitalize -}}
        {%- render 'icon' with 'chevron-down' -%}
      </button>

      <x-popover id="{{ localization_popover_id }}" initial-focus="[aria-selected='true']" class="popover popover--{{ placement | default: 'bottom-start' }} color-scheme color-scheme--dialog">
        <p class="h4" slot="header">{{ 'general.localization.language' | t }}</p>

        {%- form 'localization', id: localization_form_id -%}
          <x-listbox class="popover__value-list">
            {%- for language in localization.available_languages -%}
              {%- assign is_selected = false -%}

              {%- if language.iso_code == localization.language.iso_code -%}
                {%- assign is_selected = true -%}
              {%- endif -%}

              <button type="submit" name="locale_code" class="popover__value-option" role="option" value="{{ language.iso_code }}" aria-selected="{% if is_selected %}true{% else %}false{% endif %}">
                {{- language.endonym_name | capitalize -}}
              </button>
            {%- endfor -%}
          </x-listbox>
        {%- endform -%}
      </x-popover>
    </div>
{%- endcase -%}