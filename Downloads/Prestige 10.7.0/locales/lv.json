{"general": {"page": "{{ page }}. lappuse", "home": "Sā<PERSON><PERSON>", "accessibility": {"skip_to_content": "P<PERSON><PERSON><PERSON> uz saturu", "pagination": "<PERSON><PERSON><PERSON><PERSON> navig<PERSON>", "go_to_page": "<PERSON><PERSON><PERSON>t uz {{ index }}. lapu", "go_to_item": "P<PERSON><PERSON>t uz {{ index }}. vienumu", "item_nth_of_count": "{{ index }}. vienums no {{ count }}", "drag": "Vilkt", "close": "Aizvērt", "next": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "previous": "Atpakaļ", "play_video": "Atskaņot videoklipu", "navigate_to_next_section": "Pāriet uz nākamo sadaļu", "mute_video": "Izslēgt video skaņu", "unmute_video": "Ieslēgt video skaņu", "rss_feed": "RSS plūsma"}, "label": {"color": "<PERSON><PERSON><PERSON><PERSON>", "white": "Balts", "size": "Izmērs"}, "breadcrumb": {"label": "Atpaka<PERSON><PERSON>ļš", "home": "Sā<PERSON><PERSON>", "shop": "Veikals"}, "reviews": {"title": "Atsauk<PERSON><PERSON>"}, "social": {"follow_on": "<PERSON><PERSON> {{ social_media }}", "share": "Dalīties", "share_on": "Dalīties vietnē {{ social_media }}", "share_email": "Dalīties e-pastā"}, "rating": {"info": "{{ rating_value }} no {{ rating_max }} zvaigznēm"}, "newsletter": {"email": "E-pasts", "subscribe": "<PERSON><PERSON><PERSON><PERSON>", "notify_me": "<PERSON><PERSON><PERSON><PERSON> man", "subscribed_successfully": "<PERSON><PERSON><PERSON> mū<PERSON>."}, "localization": {"country": "Valsts", "language": "Valoda", "change_country_accessibility_text": "<PERSON><PERSON><PERSON> valsti vai valūtu", "change_language_accessibility_text": "<PERSON><PERSON><PERSON> valodu"}, "privacy_banner": {"accept": "<PERSON><PERSON><PERSON><PERSON>", "decline": "<PERSON><PERSON><PERSON><PERSON>"}, "form": {"max_characters": "{{ max_chars }} r<PERSON><PERSON><PERSON><PERSON> maks"}, "on_boarding": {"blog_post_category": "Kategorija", "blog_post_title": "Raksts", "blog_post_excerpt": "Ievadiet tekstu par savu bloga rakstu.", "product_vendor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "product_title": "Produkts", "product_description": "Ievadiet tekstu par savu bloga produktu.", "collection_title": "Kolekcija"}}, "header": {"general": {"account": "<PERSON><PERSON>", "login": "Pieteikties", "search": "Me<PERSON><PERSON><PERSON><PERSON><PERSON>", "cart": "Grozs", "primary_navigation": "<PERSON><PERSON><PERSON><PERSON><PERSON> navig<PERSON>ja", "secondary_navigation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> navigācija", "menu": "Izvēlne"}}, "product": {"general": {"description": "<PERSON><PERSON><PERSON>", "add_to_cart_button": "<PERSON><PERSON><PERSON> grozam", "pre_order_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sold_out_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unavailable_button": "Nav pieejams", "choose_options": "Izvēlieties variantus", "view_details": "<PERSON><PERSON><PERSON><PERSON>", "added_to_cart": "Pievienots grozam!", "sold_out_badge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "on_sale_badge": "<PERSON><PERSON><PERSON>", "discount_badge_html": "{{ savings }} ietaupījums", "sku": "SKU:", "variant": "Variants", "view_in_space": "View in your space", "taxes_included": "<PERSON>es<PERSON><PERSON><PERSON>.", "taxes_excluded": "Neieskaitot <PERSON>.", "shipping_policy_html": "<a href=\"{{ link }}\" class=\"link\">Piegādes izma<PERSON> tiek aprēķinātas</a> pirms apmaksas", "size_chart": "<PERSON>zm<PERSON><PERSON> tabula", "available_colors_count": {"one": "Pieejama {{ count }} kr<PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON><PERSON> {{ count }} kr<PERSON><PERSON>s"}}, "gallery": {"close": "Aizvērt gal<PERSON>", "zoom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON><PERSON><PERSON> nevar i<PERSON>"}, "price": {"regular_price": "Standarta cena", "sale_price": "Ak<PERSON>jas cena", "from_price_html": "No {{ price_min }}"}, "quantity": {"label": "Daudzums", "change_quantity": "<PERSON><PERSON><PERSON>", "increase_quantity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "decrease_quantity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minimum_of": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{ min }}", "maximum_of": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{ max }}", "increment_of": "Pieaugums {{ step }}", "cannot_add_more": "<PERSON><PERSON><PERSON><PERSON>var"}, "volume_pricing": {"title": "<PERSON><PERSON> p<PERSON> a<PERSON>", "minimum": "{{ minimum_quantity }}+", "price_at_each": "{{ price }}/gab"}, "rating_count": {"zero": "Nav at<PERSON><PERSON><PERSON><PERSON>", "one": "{{ count }} at<PERSON>uksme", "other": "{{ count }} at<PERSON>uksmes"}, "inventory": {"in_stock": "<PERSON><PERSON><PERSON>", "low_stock": "<PERSON><PERSON><PERSON><PERSON><PERSON> tika<PERSON> da<PERSON> v<PERSON>", "in_stock_with_quantity_count": {"one": "{{ count }} inventārā", "other": "{{ count }} inventārā"}, "out_of_stock": "Nav inventārā", "oversell_stock": "<PERSON><PERSON><PERSON> drīzum<PERSON>", "incoming_stock": "<PERSON><PERSON><PERSON> {{ next_incoming_date }}", "low_stock_with_quantity_count": {"one": "<PERSON><PERSON><PERSON><PERSON> tikai {{count}} v<PERSON>ī<PERSON>", "other": "<PERSON><PERSON><PERSON><PERSON><PERSON> tikai {{count}} v<PERSON><PERSON><PERSON>"}}, "store_availability": {"view_store_info": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "check_other_stores": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cito<PERSON> ve<PERSON>", "pick_up_available": "Iespēja sa<PERSON> k<PERSON>", "pick_up_currently_unavailable": "<PERSON>ņ<PERSON>ša<PERSON> k<PERSON><PERSON> pa<PERSON>k nav pieejama", "pick_up_available_at": "{{ location_name }}: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pick_up_unavailable_at": "{{ location_name }}: sa<PERSON><PERSON><PERSON><PERSON><PERSON> pa<PERSON><PERSON>k nav pieejama"}}, "collection": {"general": {"empty_collection": "<PERSON><PERSON> k<PERSON> ir tuk<PERSON>", "all_collections": "Visas kolekcijas", "no_collections": "<PERSON><PERSON><PERSON> veika<PERSON>ā nav nevienas kole<PERSON>.", "continue_shopping": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "products_count": {"zero": "0 produktu", "one": "1 produkts", "other": "{{ count }} produkti"}, "faceting": {"filters": "<PERSON><PERSON><PERSON>", "filter_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clear_filters": "<PERSON><PERSON><PERSON><PERSON><PERSON> visu", "apply_filters": "<PERSON><PERSON><PERSON><PERSON>", "sort_by": "<PERSON><PERSON><PERSON><PERSON> pēc", "remove_filter": "Noņemt filtru \"{{ name }}\"", "no_results": "Nav šiem filtriem atbilstošu produktu.", "price_range_to": "<PERSON><PERSON><PERSON><PERSON>", "price_filter_from": "<PERSON><PERSON><PERSON><PERSON><PERSON> cena", "price_filter_to": "Aug<PERSON><PERSON><PERSON><PERSON> cena", "price_filter": "{{ min_price }} - {{ max_price }}", "in_stock_only": "T<PERSON>i uzreiz <PERSON>"}}, "blog_post": {"article": "Raksts:", "share": "Dalīties", "prev": "Iepriek<PERSON><PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "blog": {"general": {"empty_blog": "Šis <PERSON> ir tuk<PERSON>s", "back_to_home": "Atpakaļ uz sākumu", "view": "<PERSON><PERSON><PERSON><PERSON>", "all_posts": "<PERSON><PERSON> r<PERSON>", "read_more": "<PERSON><PERSON><PERSON>"}, "post": {"written_by": "Autors: {{ author }}", "visit_author_website": "Apmeklēt autora vietni", "share": "Dalīties", "tags": "<PERSON><PERSON><PERSON><PERSON>"}, "comments": {"leave_comment": "<PERSON><PERSON><PERSON><PERSON> koment<PERSON>", "moderated": "Visi komentāri pirms publicēšanas tiek pārbaudīti.", "name": "<PERSON><PERSON><PERSON>", "email": "E-pasts", "message": "<PERSON><PERSON>ņ<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON><PERSON>", "comment_sent": "<PERSON><PERSON><PERSON> komentārs ir nosūtīts. Mēs to publicēsim pēc brī<PERSON>, kad to būs pārb<PERSON><PERSON> moderatori.", "comment_published": "<PERSON><PERSON><PERSON> koment<PERSON> ir public<PERSON>ts.", "count": {"zero": "{{ count }} k<PERSON><PERSON><PERSON><PERSON>", "one": "{{ count }} komentārs", "other": "{{ count }} k<PERSON><PERSON><PERSON><PERSON>"}}}, "contact": {"form": {"name": "<PERSON><PERSON><PERSON>", "email": "E-pasts", "message": "<PERSON><PERSON>ņ<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "success_message": "<PERSON><PERSON><PERSON> z<PERSON> ir nosūt<PERSON>."}}, "customer": {"account": {"title": "<PERSON><PERSON><PERSON> konts", "primary_address": "<PERSON><PERSON><PERSON><PERSON> adrese", "manage_addresses": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tagline": "Apskatiet visus savus pasūtījumus un pārvaldiet konta informāciju.", "orders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>", "no_orders": "<PERSON><PERSON><PERSON> vēl neesat veicis nevienu pasū<PERSON>.", "continue_shopping": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "login": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "instructions": "Ievadiet savu e-pasta adresi un paroli, lai pieteik<PERSON>:", "no_account": "Vai jums nav konta?", "email": "E-pasts", "password": "Parole", "submit": "Pieteikties", "forgot_password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>?", "sign_up": "Reģistrēties"}, "recover_password": {"title": "<PERSON><PERSON><PERSON><PERSON> paroli", "instructions": "Ievadiet savu e-pasta adresi, lai atg<PERSON><PERSON> paroli:", "remember_password": "Vai atceraties savu paroli?", "email": "E-pasts", "submit": "<PERSON><PERSON><PERSON><PERSON>", "back_to_login": "Atpakaļ uz pieteikšanos", "success_message": "Uz jūsu adresi ir nosūtīts e-pasta ziņojums ar instrukcijām paroles atiestatīšanai."}, "register": {"title": "Reģistrēties", "instructions": "<PERSON><PERSON><PERSON><PERSON>, aizpild<PERSON> zemāk norā<PERSON><PERSON><PERSON>:", "already_have_account": "Vai jums jau ir konts?", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "Uzvārds", "email": "E-pasts", "password": "Parole", "accepts_marketing": "Reģistrējieties mūsu bi<PERSON>nam", "submit": "Izveidot kontu", "login": "Pieteikties"}, "activate_account": {"title": "Konta aktivizēšana", "instructions": "<PERSON>evadiet paroli, lai izveidotu savu kontu:", "password": "Parole", "password_confirmation": "<PERSON><PERSON><PERSON>", "submit": "Aktivizēt", "cancel": "Atcelt"}, "reset_password": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> paroli", "instructions": "Ievadiet jaunu paroli:", "password": "Parole", "password_confirmation": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "order": {"order": "Pasūtījums", "created_at": "Pasūtījums veikts {{created_at}}", "order_name": "Pa<PERSON><PERSON>tīju<PERSON> {{name}}", "view_details": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "back": "Atpakaļ", "date": "Datums", "payment_status": "Maksājuma statuss", "fulfillment_status": "Izpildes statuss", "cancelled_on": "Atcelts {{date}}. Iemesls: {{reason}}", "product": "Produkts", "quantity": "Daudzums", "fulfillment_with_number": "<PERSON><PERSON><PERSON> pasūtījums ir nosūtīts {{date}}. Izsekojiet sūt<PERSON>, izman<PERSON><PERSON>t numuru {{tracking_number}}.", "fulfillment": "<PERSON><PERSON><PERSON> pasūtījums ir nosūtīts {{date}}.", "track_shipment": "Izsekot sūtījumu", "subtotal": "Starpsumma", "discount": "<PERSON><PERSON><PERSON>", "shipping": "<PERSON><PERSON><PERSON><PERSON>", "taxes_included": "<PERSON>eskai<PERSON><PERSON>", "taxes_excluded": "Neieskai<PERSON><PERSON>", "total_duties": "Nodokļi", "refunded_amount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> summa", "total": "Kopā", "shipping_address": "<PERSON><PERSON><PERSON><PERSON> adrese", "billing_address": "Norēķinu adrese"}, "addresses": {"title": "<PERSON><PERSON><PERSON>", "no_addresses": "Jums vēl nav saglabāta neviena adrese.", "add_address": "<PERSON><PERSON><PERSON>", "edit_address": "Rediģēt adresi", "save_address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": "Rediģēt", "delete": "<PERSON><PERSON><PERSON><PERSON>", "default_address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> adrese", "address_title": "{{ position }}. adrese", "delete_confirm": "Vai noņemt šo adresi? Šī darbība pēc apstiprin<PERSON> būs neatgriezen<PERSON>a.", "fill_form": "<PERSON><PERSON><PERSON><PERSON>, aizpild<PERSON> zemāk norā<PERSON><PERSON><PERSON>:", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "Uzvārds", "company": "Uzņēmums", "phone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> numurs", "address1": "Adreses 1. r<PERSON><PERSON><PERSON><PERSON>", "address2": "Adreses 2. <PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Pilsē<PERSON>", "zip": "Pasta indekss", "country": "Valsts", "province": "Province, reģions", "set_default": "Iestatīt kā noklusējuma adresi"}}, "cart": {"general": {"title": "Grozs", "empty": "<PERSON><PERSON><PERSON> gro<PERSON> ir tuk<PERSON>", "continue_shopping": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "weight": "<PERSON><PERSON><PERSON>", "subtotal": "Starpsumma", "total": "Kopā", "add_order_note": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edit_order_note": "Rediģēt pasūt<PERSON><PERSON>ma <PERSON>", "order_note": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "save_note": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "note_placeholder": "Kā varam palīd<PERSON>t?", "update_cart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> grozu", "view_cart": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>zu", "checkout": "Noformēt pasūtījumu", "we_accept": "<PERSON><PERSON><PERSON>", "taxes_and_shipping_policy_at_checkout_html": "Nodokļi un <a href=\"{{ link }}\" class=\"link\">piegā<PERSON> izma<PERSON>as tiek aprēķinātas</a> pirms apmaksas", "taxes_included_but_shipping_at_checkout": "Nodok<PERSON><PERSON> ir i<PERSON><PERSON>, un piegādes izmaksas tiks aprēķinātas pirms apmaksas", "taxes_included_and_shipping_policy_html": "Nodokļi iekļauti. <a href=\"{{ link }}\" class=\"link\"><PERSON>g<PERSON><PERSON> i<PERSON></a> tiek aprēķinātas pirms apmaksas.", "taxes_and_shipping_at_checkout": "Nodokļi un piegādes izmaksas tiks aprēķinātas pirms apmaksas"}, "order": {"product": "Produkts", "total": "Kopā", "quantity": "Daudzums", "remove": "<PERSON><PERSON><PERSON><PERSON>", "remove_with_title": "Noņemt {{ title }}"}, "free_shipping_bar": {"limit_unreached_html": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>et vēl {{ remaining_amount }} un saņemiet bezmaksas piegādi!", "limit_reached_html": "<PERSON><PERSON><PERSON> varat sa<PERSON>t bez<PERSON> pieg<PERSON>."}, "shipping_estimator": {"estimate_shipping": "Aprēķināt piegā<PERSON> i<PERSON>as", "country": "Valsts", "province": "Province, reģions", "zip": "Pasta indekss", "estimate": "Aprēķināt", "no_results": "Die<PERSON><PERSON><PERSON><PERSON> mēs neveicam piegādes uz jūsu adresi.", "one_result": "<PERSON><PERSON><PERSON> adresē ir pieejams viens piegādes tarifs:", "multiple_results": "<PERSON><PERSON><PERSON> ad<PERSON> ir pieejami vairāk piegādes tarifi:", "error": "Aprēķinot piegādes tarifus, radās viena vai vairākas k<PERSON>das:"}}, "404": {"general": {"title": "404", "continue_shopping": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "not_found_info": "<PERSON><PERSON><PERSON>m atrast jūsu mek<PERSON>to lapu. <PERSON><PERSON><PERSON><PERSON>, izman<PERSON>jiet navigāciju vai zemāk redzamo pogu, lai atgrieztos mūsu tīmekļa vietnē."}}, "search": {"general": {"title": "Me<PERSON><PERSON><PERSON><PERSON><PERSON>", "terms": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"{{ terms }}\"", "search_placeholder": "Meklējiet...", "products": "Produktus", "suggestions": "<PERSON><PERSON><PERSON><PERSON>", "collections": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "posts": "<PERSON><PERSON><PERSON> r<PERSON>", "pages": "Lapas", "clear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "view_all_results": "<PERSON><PERSON><PERSON><PERSON> visus rezultātus", "no_results": "<PERSON><PERSON><PERSON><PERSON><PERSON> netika atrasti."}, "results_count": {"zero": "0 rezultātu", "one": "1 rezultāts", "other": "{{ count }} re<PERSON><PERSON><PERSON><PERSON>"}, "results_count_for_terms": {"zero": "0 rezultātu meklējumam \"{{ terms }}\"", "one": "1 rezultāts meklējumam \"{{ terms }}\"", "other": "{{ count }} rezult<PERSON><PERSON> \"{{ terms }}\""}}, "gift_card": {"general": {"title": "<PERSON><PERSON><PERSON>, jūsu dāvanu karte", "copy": "<PERSON><PERSON><PERSON><PERSON>", "print": "<PERSON><PERSON><PERSON><PERSON>"}, "issued": {"remaining_amount": "<PERSON><PERSON><PERSON><PERSON> summa", "redeem_instructions": "<PERSON><PERSON><PERSON><PERSON><PERSON>o kodu, lai a<PERSON><PERSON><PERSON><PERSON> pasūtījumu ar dāvanu karti:", "code": "<PERSON>āvanu kartes kods", "expires_on": "<PERSON><PERSON><PERSON><PERSON> termiņš: {{ expires_on }}", "expired": "<PERSON><PERSON><PERSON> dāvanu karte vairs nav derīga vai arī ir bloķēta.", "scan": "vai arī sken<PERSON>jiet šo QR kodu", "add_to_apple_wallet": "<PERSON>vienot Apple Wallet", "amount": "Summa: {{ initial_value }}", "copied": "Nokopēts!"}, "recipient": {"checkbox": "<PERSON><PERSON><PERSON> šo nosūtīt kā dāvanu", "email_label": "Saņēmēja e-pasts", "name_label": "<PERSON>ņ<PERSON><PERSON><PERSON><PERSON> v<PERSON> (neobligāti)", "send_on_label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (pēc izvēles)", "message_label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (pēc izvēles)"}}, "password": {"general": {"follow_us": "<PERSON><PERSON><PERSON><PERSON> mums", "powered_by": "<PERSON><PERSON> ve<PERSON>la darb<PERSON>", "store_owner": "Esat ve<PERSON>la īpašnieks?", "login": "Pieteikties"}, "storefront_access": {"enter_password": "Ienā<PERSON> ar paroli", "store_access": "Pie<PERSON><PERSON><PERSON>", "instructions": "<PERSON><PERSON><PERSON><PERSON>, lai pie<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "password": "Parole", "enter_store": "Ienākt veikalā"}}, "sections": {"before_after": {"not_enough_blocks": "<PERSON> sa<PERSON> “pirms/pēc”, iekļ<PERSON><PERSON><PERSON> sadaļ<PERSON> vairāk bloku.", "percentage_shown": "Par<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>pēc” attēla da<PERSON> pro<PERSON>", "cursor_accessibility_text": "<PERSON>zman<PERSON>jiet kreiso un labo bulttaustiņus, lai pār<PERSON><PERSON><PERSON> starp pirms un pēc fotoattēliem."}, "countdown_timer": {"expires_accessibility_text": "Piedāvā<PERSON><PERSON> ir spēkā līdz {{expiration_date}}", "days": "<PERSON><PERSON>", "hours": "h", "minutes": "m", "seconds": "s", "days_long": "<PERSON><PERSON>", "hours_long": "Stundas", "minutes_long": "<PERSON><PERSON><PERSON>", "seconds_long": "<PERSON><PERSON><PERSON>"}, "shop_the_look": {"view_products": "<PERSON><PERSON><PERSON><PERSON> produktus", "view_product": "<PERSON><PERSON><PERSON><PERSON> produktu"}, "quick_order_list": {"products": "Produkti", "variants": "<PERSON><PERSON><PERSON>", "quantity": "Daudzums", "price": "<PERSON><PERSON>", "total": "Kopā", "remove": "<PERSON><PERSON><PERSON><PERSON>", "quantity_rules": "<PERSON><PERSON><PERSON><PERSON>", "volume_pricing": "<PERSON><PERSON><PERSON><PERSON> cenas", "volume_pricing_available": "<PERSON><PERSON><PERSON><PERSON> cena<PERSON>", "show_variants": "<PERSON><PERSON><PERSON><PERSON><PERSON> variantus", "minimum_of": "Minimums: {{ min }}", "maximum_of": "Maksimums: {{ max }}", "increment_of": "Palielinājums: {{ step }}", "view_cart": "<PERSON><PERSON><PERSON><PERSON><PERSON> grozu", "remove_all": "Noņemt visu", "item_count": {"zero": "{{ count }} preces", "one": "{{ count }} prece", "other": "{{ count }} preces"}, "product_subtotal": "Produktu starpsumma"}}}