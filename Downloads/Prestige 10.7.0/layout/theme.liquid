<!doctype html>

<html lang="{{ request.locale.iso_code }}" dir="{% render 'direction' %}">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, height=device-height, minimum-scale=1.0, maximum-scale=5.0">

    <title>{% if page_title == blank %}{{ shop.name }}{% else %}{{ page_title }}{% if current_page != 1 %} &ndash; {{ 'general.page' | t: page: current_page }}{% endif %}{% endif %}</title>

    {%- if page_description -%}
      <meta name="description" content="{{ page_description | escape }}">
    {%- endif -%}

    <link rel="canonical" href="{{ canonical_url }}">

    {%- if settings.favicon -%}
      <link rel="shortcut icon" href="{{ settings.favicon | image_url: width: 96 }}">
      <link rel="apple-touch-icon" href="{{ settings.favicon | image_url: width: 180 }}">
    {%- endif -%}

    {%- comment -%}Few prefetch to increase performance on commonly used third-parties{%- endcomment -%}
    <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>

    {%- unless settings.heading_font.system? -%}
      <link rel="preload" href="{{ settings.heading_font | font_url }}" as="font" type="font/woff2" crossorigin>
    {%- endunless -%}

    {%- unless settings.text_font.system? -%}
      <link rel="preload" href="{{ settings.text_font | font_url }}" as="font" type="font/woff2" crossorigin>
    {%- endunless -%}

    {%- render 'social-meta-tags' -%}
    {%- render 'microdata-schema' -%}
    {%- render 'css-variables' -%}
    {%- render 'js-variables' -%}

    {%- if request.page_type == 'gift_card' -%}
      <script src="{{ 'vendor/qrcode.js' | shopify_asset_url }}" defer></script>
    {%- endif -%}

    <script type="importmap">
      {%- comment -%}On Safari 16.3 and lower, a polyfill is used to load importmap{%- endcomment -%}
      {
        "imports": {
          "vendor": "{{ 'vendor.min.js' | asset_url }}",
          "theme": "{{ 'theme.js' | asset_url }}",
          "photoswipe": "{{ 'photoswipe.min.js' | asset_url }}"
        }
      }
    </script>

    <script type="module" src="{{ 'vendor.min.js' | asset_url }}"></script>
    <script type="module" src="{{ 'theme.js' | asset_url }}"></script>

    {{ content_for_header }}

    {{- 'theme.css' | asset_url | stylesheet_tag: preload: true -}}
  </head>

  {% liquid
    assign features_class = ''
  
    if settings.show_button_transition
      assign features_class = features_class | append: 'features--button-transition '
    endif
  
    if settings.show_image_zoom_on_hover
      assign features_class = features_class | append: 'features--zoom-image '
    endif
  %}

  <body class="{{ features_class }} color-scheme color-scheme--{{ settings.default_color_scheme.id }}">
    {%- render 'shadow-dom-templates' -%}

    <loading-bar class="loading-bar" aria-hidden="true"></loading-bar>
    <a href="#main" allow-hash-change class="skip-to-content sr-only">{{ 'general.accessibility.skip_to_content' | t }}</a>

    <span id="header-scroll-tracker" style="position: absolute; width: 1px; height: 1px; top: var(--header-scroll-tracker-offset, 10px); left: 0;">
      {%- comment -%}
        This allows our theme to track when the user has scrolled a given amount of pixels, without relying on a global scroll listener. This helps
        to improve performance and reduce reflows.
      {%- endcomment -%}
    </span>

    {%- if request.page_type != 'password' -%}
      {%- sections 'header-group' -%}
      {%- sections 'overlay-group' -%}
    {%- endif -%}

    <main id="main" class="anchor">
      {{ content_for_layout }}

      {%- comment -%}
      IMPLEMENTATION NOTE: For best semantics, having the footer group outside the main would be slightly better, but
        moving it inside the main allows to have all the sections (including sections inside the footer group) to be
        consecutive, and hence having a cleaner margin collapsing management.
      {%- endcomment -%}
      {%- if request.page_type != 'password' -%}
        {%- sections 'footer-group' -%}
      {%- endif -%}
    </main>
  </body>
</html>
